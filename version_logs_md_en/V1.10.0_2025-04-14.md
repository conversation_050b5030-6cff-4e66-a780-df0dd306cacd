# V1.10.0 Release Notes

- [Feature] Added built-in assignment node
- [Feature] Enhanced built-in plugins to support system-level configuration for specific spaces
- [Feature] Expanded API plugin configuration to support multiple API configurations
- [Feature] Added credential management page support
- [Feature] Added task cleanup functionality
- [Feature] API plugin requests now include username
- [Feature] Added secondary confirmation and isolation for BlueWhale plugin authorization
- [Feature] Support for copying process templates in management console
- [Bugfix] Added TRACEPARENT to request headers
- [Bugfix] Fixed canvas node pasting issues
- [Bugfix] Updated API documentation
- [Bugfix] Fixed issue with empty log queries in PaaS3 environment
- [Bugfix] Fixed issue with space administrator overstepping permissions
- [Bugfix] API plugin requests now hide sensitive information in headers
- [Bugfix] Decision tables do not currently support input values with English double quotes
