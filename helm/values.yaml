# Default values for bkflow.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# 全局配置
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

# 镜像配置
#image:
#  registry: hub.bktencent.com
#  repository: blueking/bkflow
#  tag: "1.11.1"
#  pullPolicy: IfNotPresent
image:
  registry: ""          # 留空，不使用远程仓库
  repository: bkflow-python_3.11  # 你的本地镜像名（不含 tag）
  tag: "latest"         # 你的本地镜像 tag
  pullPolicy: Never     # 关键！禁止 Helm 尝试拉取远程镜像


# Service Account 配置
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod 安全上下文
podSecurityContext:
  fsGroup: 1000

# 容器安全上下文
securityContext:
  runAsNonRoot: true
  runAsUser: 1000
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: false
  allowPrivilegeEscalation: false

# BKFlow 应用配置
app:
  bkAppCode: bk_flow_engine
  bkAppName: "蓝鲸流程引擎服务"

  # 内部 Token（生产环境应该通过 Secret 注入）
  appInternalToken: "df1da1fc5b134858b0743838d7db256b"
  defaultEngineInternalToken: "a35230d7a282485dba66a867e07d8125"
  defaultCallbackKey: "jbSH1_3PFsM8WRZZpUXJPhlJuvuA44A7Ov0nPhFk5ZY="

  # APIGW 配置
  apigwName: bk_flow_engine
  apigwNetlocPattern: "^bkapi.{hostname}"

  # 插件服务
  usePluginService: true

# 模块配置
modules:
  # Interface 模块 - 入口模块，管理空间、流程、凭证等上层资源
  interface:
    enabled: true
    moduleType: interface
    replicas: 2

    # Web 服务配置
    web:
      enabled: true
      port: 5000
      gunicorn:
        workerNum: 4
        threadNum: 10

    # Celery 进程配置
    processes:
      celerybeat:
        enabled: true
        replicas: 1
        command: "celery -A blueapps.core.celery beat -l info"
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 512Mi

      celeryworker:
        enabled: true
        replicas: 2
        command: "celery -A blueapps.core.celery worker -n interface_worker@%h -P threads -c 100 -l info"
        resources:
          limits:
            cpu: 2000m
            memory: 2Gi
          requests:
            cpu: 1000m
            memory: 1Gi

    # 资源配置
    resources:
      limits:
        cpu: 2000m
        memory: 2Gi
      requests:
        cpu: 1000m
        memory: 1Gi


  # Engine 模块 - 任务执行模块，与空间强绑定
  engine:
    enabled: true
    moduleType: engine
    moduleCode: default  # 可以通过 space_id 进行路由
    replicas: 2

    # Web 服务配置
    web:
      enabled: true
      port: 5000
      gunicorn:
        workerNum: 2
        threadNum: 10

    # Engine 特有的进程配置
    processes:
      # 执行引擎 - 执行任务
      er_e:
        enabled: true
        replicas: 5
        command: "celery -A blueapps.core.celery worker -P threads -Q er_execute_${BKFLOW_MODULE_CODE} -n er_e_worker@%h -c 100 -l info"
        resources:
          limits:
            cpu: 4000m
            memory: 4Gi
          requests:
            cpu: 2000m
            memory: 2Gi

      # 调度引擎 - 调度任务
      er_s:
        enabled: true
        replicas: 5
        command: "celery -A blueapps.core.celery worker -P threads -Q er_schedule_${BKFLOW_MODULE_CODE} -n er_s_worker@%h -c 100 -l info"
        resources:
          limits:
            cpu: 4000m
            memory: 4Gi
          requests:
            cpu: 2000m
            memory: 2Gi

      # 通用 Worker
      cworker:
        enabled: true
        replicas: 5
        command: "python manage.py celery worker -Q celery,pipeline_additional_task,pipeline_additional_task_priority,task_common_${BKFLOW_MODULE_CODE},node_auto_retry_${BKFLOW_MODULE_CODE},timeout_node_execute_${BKFLOW_MODULE_CODE},timeout_node_record_${BKFLOW_MODULE_CODE} -n common_worker@%h -P threads -c 10 -l info"
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 512Mi

      # 超时处理
      timeout:
        enabled: true
        replicas: 1
        command: "python manage.py node_timeout_process"
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 512Mi

      # Beat 调度器
      beat:
        enabled: true
        replicas: 1
        command: "celery -A blueapps.core.celery beat -l info"
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 512Mi

      # 清理 Worker
      clean_worker:
        enabled: true
        replicas: 1
        command: "celery -A blueapps.core.celery worker -P threads -Q clean_task_${BKFLOW_MODULE_CODE} -n clean_worker@%h -c 100 -l info"
        resources:
          limits:
            cpu: 4000m
            memory: 4Gi
          requests:
            cpu: 2000m
            memory: 2Gi

    # 资源配置
    resources:
      limits:
        cpu: 2000m
        memory: 2Gi
      requests:
        cpu: 1000m
        memory: 1Gi

# 环境变量配置
env:
  # BKFlow 核心配置
  DJANGO_SETTINGS_MODULE: "settings"
  PYTHONPATH: "/app"
  BK_APP_CODE: "bk_flow_engine"
  BK_APIGW_NAME: "bk_flow_engine"
  BK_APIGW_NETLOC_PATTERN: "^bkapi.{hostname}"
  BKAPP_USE_PLUGIN_SERVICE: "1"

# 服务配置
service:
  type: ClusterIP
  port: 80
  targetPort: 5000
  annotations: {}

# Ingress 配置
ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: bkflow.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

# 持久化配置
persistence:
  enabled: false
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 8Gi

# 节点选择器、容忍度和亲和性
nodeSelector: {}
tolerations: []
affinity: {}

# Pod 注解和标签
podAnnotations: {}
podLabels: {}

# 自动扩缩容配置
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80

# 健康检查配置
livenessProbe:
  enabled: true
  httpGet:
    path: /healthz
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3

readinessProbe:
  enabled: true
  httpGet:
    path: /healthz
    port: http
  initialDelaySeconds: 5
  periodSeconds: 5
  timeoutSeconds: 3
  failureThreshold: 3

# 依赖服务配置
mysql:
  enabled: true
  auth:
    rootPassword: "bkflow123"
    database: "bkflow"
    username: "bkflow"
    password: "bkflow123"
  primary:
    persistence:
      enabled: true
      size: 8Gi

rabbitmq:
  enabled: true
  auth:
    username: "bkflow"
    password: "bkflow123"
  persistence:
    enabled: true
    size: 8Gi

redis:
  enabled: true
  auth:
    enabled: true
    password: "bkflow123"
  master:
    persistence:
      enabled: true
      size: 8Gi

# 外部数据库配置（当 mysql.enabled=false 时使用）
externalDatabase:
  enabled: false
  host: ""
  port: 3306
  username: ""
  password: ""
  database: ""

# 日志采集配置
bkLogConfig:
  enabled: false
  dataId: 1
