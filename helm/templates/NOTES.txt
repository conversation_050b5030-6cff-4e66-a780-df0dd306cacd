🎉 BKFlow has been deployed successfully!

📋 Release Information:
   - Release Name: {{ .Release.Name }}
   - Namespace: {{ .Release.Namespace }}
   - Chart Version: {{ .Chart.Version }}
   - App Version: {{ .Chart.AppVersion }}

🚀 Deployed Modules:
{{- if .Values.modules.interface.enabled }}
   ✅ Interface Module (Entry point for spaces, processes, credentials management)
      - Replicas: {{ .Values.modules.interface.replicas }}
      - Celery Beat: {{ if .Values.modules.interface.processes.celerybeat.enabled }}Enabled{{ else }}Disabled{{ end }}
      - Celery Worker: {{ if .Values.modules.interface.processes.celeryworker.enabled }}Enabled{{ else }}Disabled{{ end }}
{{- end }}

{{- if .Values.modules.engine.enabled }}
   ✅ Engine Module (Task execution module, space-bound routing)
      - Module Code: {{ .Values.modules.engine.moduleCode }}
      - Replicas: {{ .Values.modules.engine.replicas }}
      - ER Execute Workers: {{ if .Values.modules.engine.processes.er_e.enabled }}{{ .Values.modules.engine.processes.er_e.replicas }} replicas{{ else }}Disabled{{ end }}
      - ER Schedule Workers: {{ if .Values.modules.engine.processes.er_s.enabled }}{{ .Values.modules.engine.processes.er_s.replicas }} replicas{{ else }}Disabled{{ end }}
      - Common Workers: {{ if .Values.modules.engine.processes.cworker.enabled }}{{ .Values.modules.engine.processes.cworker.replicas }} replicas{{ else }}Disabled{{ end }}
      - Timeout Workers: {{ if .Values.modules.engine.processes.timeout.enabled }}{{ .Values.modules.engine.processes.timeout.replicas }} replicas{{ else }}Disabled{{ end }}
      - Beat Scheduler: {{ if .Values.modules.engine.processes.beat.enabled }}Enabled{{ else }}Disabled{{ end }}
      - Clean Workers: {{ if .Values.modules.engine.processes.clean_worker.enabled }}{{ .Values.modules.engine.processes.clean_worker.replicas }} replicas{{ else }}Disabled{{ end }}
{{- end }}

🔗 Access Information:
{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  {{- range .paths }}
   🌐 External URL: http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}
{{- else if contains "NodePort" .Values.service.type }}
   🔧 NodePort Access:
   export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "bkflow.fullname" . }}-interface)
   export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
   echo "Interface: http://$NODE_IP:$NODE_PORT"
{{- else if contains "LoadBalancer" .Values.service.type }}
   ⚖️  LoadBalancer Access:
   NOTE: It may take a few minutes for the LoadBalancer IP to be available.
   kubectl get --namespace {{ .Release.Namespace }} svc -w {{ include "bkflow.fullname" . }}-interface

   export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ include "bkflow.fullname" . }}-interface --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
   echo "Interface: http://$SERVICE_IP:{{ .Values.service.port }}"
{{- else if contains "ClusterIP" .Values.service.type }}
   🔧 Port Forward Access:
   # Interface Module
   kubectl --namespace {{ .Release.Namespace }} port-forward svc/{{ include "bkflow.fullname" . }}-interface {{ .Values.service.port }}:{{ .Values.service.port }}
   echo "Interface: http://127.0.0.1:{{ .Values.service.port }}"

   {{- if .Values.modules.engine.enabled }}
   # Engine Module
   kubectl --namespace {{ .Release.Namespace }} port-forward svc/{{ include "bkflow.fullname" . }}-engine {{ .Values.service.port }}:{{ .Values.service.port }}
   echo "Engine: http://127.0.0.1:{{ .Values.service.port }}"
   {{- end }}
{{- end }}

📊 Dependencies:
{{- if .Values.mysql.enabled }}
   ✅ MySQL: Enabled (Internal)
{{- else }}
   ⚠️  MySQL: Using external database
{{- end }}
{{- if .Values.rabbitmq.enabled }}
   ✅ RabbitMQ: Enabled (Internal)
{{- else }}
   ⚠️  RabbitMQ: Using external message queue
{{- end }}
{{- if .Values.redis.enabled }}
   ✅ Redis: Enabled (Internal)
{{- else }}
   ⚠️  Redis: Using external cache
{{- end }}

📝 Useful Commands:
   # Check pod status
   kubectl get pods -n {{ .Release.Namespace }} -l app.kubernetes.io/instance={{ .Release.Name }}

   # View logs
   kubectl logs -n {{ .Release.Namespace }} -l app.kubernetes.io/instance={{ .Release.Name }} -f

   # Scale interface module
   kubectl scale deployment {{ include "bkflow.fullname" . }}-interface -n {{ .Release.Namespace }} --replicas=3

📚 Documentation: https://github.com/TencentBlueKing/bk-flow
