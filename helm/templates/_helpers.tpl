{{/*
Expand the name of the chart.
*/}}
{{- define "bkflow.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "bkflow.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bkflow.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "bkflow.labels" -}}
helm.sh/chart: {{ include "bkflow.chart" . }}
{{ include "bkflow.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "bkflow.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bkflow.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "bkflow.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "bkflow.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Create the image name
*/}}
{{- define "bkflow.image" -}}
{{- $registry := .Values.global.imageRegistry | default .Values.image.registry -}}
{{- $repository := .Values.image.repository -}}
{{- $tag := .Values.image.tag | default .Chart.AppVersion -}}
{{- if $registry }}
{{- printf "%s/%s:%s" $registry $repository $tag }}
{{- else }}
{{- printf "%s:%s" $repository $tag }}
{{- end }}
{{- end }}

{{/*
MySQL fullname
*/}}
{{- define "bkflow.mysql.fullname" -}}
{{- printf "%s-mysql" (include "bkflow.fullname" .) }}
{{- end }}

{{/*
MySQL secret name
*/}}
{{- define "bkflow.mysql.secretName" -}}
{{- printf "%s-mysql" (include "bkflow.fullname" .) }}
{{- end }}

{{/*
RabbitMQ fullname
*/}}
{{- define "bkflow.rabbitmq.fullname" -}}
{{- printf "%s-rabbitmq" (include "bkflow.fullname" .) }}
{{- end }}

{{/*
RabbitMQ secret name
*/}}
{{- define "bkflow.rabbitmq.secretName" -}}
{{- printf "%s-rabbitmq" (include "bkflow.fullname" .) }}
{{- end }}

{{/*
Redis fullname
*/}}
{{- define "bkflow.redis.fullname" -}}
{{- printf "%s-redis" (include "bkflow.fullname" .) }}
{{- end }}

{{/*
Redis secret name
*/}}
{{- define "bkflow.redis.secretName" -}}
{{- printf "%s-redis" (include "bkflow.fullname" .) }}
{{- end }}

{{/*
Common environment variables for all deployments
*/}}
{{- define "bkflow.commonEnv" -}}
{{- range $k, $v := .Values.env }}
- name: {{ $k }}
  value: {{ $v | quote }}
{{- end }}
{{- if .Values.mysql.enabled }}
- name: MYSQL_HOST
  value: {{ include "bkflow.mysql.fullname" . }}
- name: MYSQL_PORT
  value: "3306"
- name: MYSQL_USER
  value: {{ .Values.mysql.auth.username | quote }}
- name: MYSQL_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow.mysql.secretName" . }}
      key: mysql-password
- name: MYSQL_DATABASE
  value: {{ .Values.mysql.auth.database | quote }}
{{- end }}
{{- if .Values.rabbitmq.enabled }}
- name: RABBITMQ_HOST
  value: {{ include "bkflow.rabbitmq.fullname" . }}
- name: RABBITMQ_PORT
  value: "5672"
- name: RABBITMQ_USER
  value: {{ .Values.rabbitmq.auth.username | quote }}
- name: RABBITMQ_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow.rabbitmq.secretName" . }}
      key: rabbitmq-password
{{- end }}
{{- if .Values.redis.enabled }}
- name: REDIS_HOST
  value: {{ include "bkflow.redis.fullname" . }}-master
- name: REDIS_PORT
  value: "6379"
{{- if .Values.redis.auth.enabled }}
- name: REDIS_PASSWORD
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow.redis.secretName" . }}
      key: redis-password
{{- end }}
{{- end }}
- name: APP_INTERNAL_TOKEN
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow.fullname" . }}-secret
      key: app-internal-token
- name: DEFAULT_ENGINE_APP_INTERNAL_TOKEN
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow.fullname" . }}-secret
      key: default-engine-internal-token
- name: BKFLOW_DEFAULT_CALLBACK_KEY
  valueFrom:
    secretKeyRef:
      name: {{ include "bkflow.fullname" . }}-secret
      key: default-callback-key
{{- end }}
