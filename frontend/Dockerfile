

# -----------------------------------------------------------------------------
# Stage 1: Build Stage
# -----------------------------------------------------------------------------
FROM python:3.11-slim as builder

LABEL maintainer="chiayzhang <<EMAIL>>"

ENV PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on

WORKDIR /app

RUN apt-get update && \
    apt-get install -y --no-install-recommends build-essential && \
    rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .

# -----------------------------------------------------------------------------
# Stage 2: Final "Pure" Stage
# -----------------------------------------------------------------------------
FROM python:3.11-slim

WORKDIR /app

COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin
COPY --from=builder /app .

RUN mv ./bin/container_start.sh ./container_start.sh

# --- 修复换行符问题 ---
RUN sed -i 's/\r$//' ./container_start.sh
RUN chmod +x ./container_start.sh

# --- 声明所有必须在运行时提供的环境变量 ---
# 启动脚本将负责验证这些变量是否已被注入。
# Django 和模块配置
ENV DJANGO_SETTINGS_MODULE="settings"
ENV PYTHONPATH="/app/src"
ENV BKFLOW_MODULE_TYPE="interface"

# 端口
ENV PORT="5000"

# 赋予脚本执行权限
RUN chmod +x ./*.sh

# 暴露端口
EXPOSE ${PORT}

# 定义容器启动命令
# 启动脚本将负责验证环境变量并启动 Gunicorn
CMD ["./container_start.sh"]
