const cn = {
  '1. 若子流程中增加了新的变量，在未手动更新子流程版本的情况下，将使用新变量默认值。': '1. 若子流程中增加了新的变量，在未手动更新子流程版本的情况下，将使用新变量默认值。',
  '2. 若子流程中修改了变量的默认值，在未手动更新子流程版本的情况下，将继续使用修改前变量的原有值。': '2. 若子流程中修改了变量的默认值，在未手动更新子流程版本的情况下，将继续使用修改前变量的原有值。',
  接口数据返回为空: '接口数据返回为空',
  '接口异常，': '接口异常，',
  'HTTP状态码：': 'HTTP状态码：',
  分类: '分类',
  api列表: 'api列表',
  '统一 API 列表搜索功能需要 API 提供方支持，如果提供方不支持，则列表过滤不生效': '统一 API 列表搜索功能需要 API 提供方支持，如果提供方不支持，则列表过滤不生效',
  请求方法: '请求方法',
  api参数: 'api参数',
  取消变量引用: '取消变量引用',
  设置为变量: '设置为变量',
  必填项: '必填项',
  暂无数据: '暂无数据',
  '可以尝试 调整关键词 或': '可以尝试 调整关键词 或',
  清空筛选条件: '清空筛选条件',
  复制: '复制',
  退出: '退出',
  全屏: '全屏',
  '按 Esc 即可退出全屏模式': '按 Esc 即可退出全屏模式',
  分: '分',
  时: '时',
  日: '日',
  月: '月',
  周: '周',
  '长度超过100个字符，请修改规则': '长度超过100个字符，请修改规则',
  '使用了除“,-*/”以外的特殊字符，请修改规则': '使用了除“,-*/”以外的特殊字符，请修改规则',
  以及当月: '以及当月',
  '下次：': '下次：',
  选择生成: '选择生成',
  手动输入: '手动输入',
  清空: '清空',
  天: '天',
  '0 表示星期天，6 表示星期六': '0 表示星期天，6 表示星期六',
  分钟: '分钟',
  小时: '小时',
  星期: '星期',
  日期: '日期',
  月份: '月份',
  循环: '循环',
  从第: '从第',
  从星期: '从星期',
  '开始，每隔': '开始，每隔',
  执行一次: '执行一次',
  指定: '指定',
  星期一: '星期一',
  星期二: '星期二',
  星期三: '星期三',
  星期四: '星期四',
  星期五: '星期五',
  星期六: '星期六',
  星期天: '星期天',
  访问异常: '访问异常',
  仅提供从已认证平台发起访问: '仅提供从已认证平台发起访问',
  不支持直接请求本系统页面: '不支持直接请求本系统页面',
  '如有接入需求，请联系管理员': '如有接入需求，请联系管理员',
  找不到页面: '找不到页面',
  '对不起，您没有当前应用的管理员权限': '对不起，您没有当前应用的管理员权限',
  '请尝试如下操作：': '请尝试如下操作：',
  '联系“管理员”为您添加管理员权限': '联系“管理员”为您添加管理员权限',
  应用出现异常: '应用出现异常',
  '系统出现异常, 请记录下错误场景并与开发人员联系, 谢谢!': '系统出现异常, 请记录下错误场景并与开发人员联系, 谢谢!',
  收起: '收起',
  未能重用: '未能重用',
  将沿用原参数值提交: '将沿用原参数值提交',
  知道了: '知道了',
  取消变量免渲染: '取消变量免渲染',
  变量免渲染: '变量免渲染',
  展开全部: '展开全部',
  类型: '类型',
  '请输入IP，多个以逗号或者换行符隔开，在cmdb上唯一': '请输入IP，多个以逗号或者换行符隔开，在cmdb上唯一',
  '请输入业务拓扑（形如：业务A>集群B>模块C），多个逗号或换行符隔开，在cmdb上唯一': '请输入业务拓扑（形如：业务A>集群B>模块C），多个逗号或换行符隔开，在cmdb上唯一',
  '请输入动态分组名称，多个以逗号或换行符隔开，在cmdb上唯一': '请输入动态分组名称，多个以逗号或换行符隔开，在cmdb上唯一',
  表单保存数据与最新的CMDB: '表单保存数据与最新的CMDB',
  '配置存在差异，是否更新变量数据？': '配置存在差异，是否更新变量数据？',
  确认: '确认',
  '变量保存数据与最新的CMDB集群配置存在差异，是否更新变量数据？': '变量保存数据与最新的CMDB集群配置存在差异，是否更新变量数据？',
  请选择日期时间: '请选择日期时间',
  禁用选择器: '禁用选择器',
  已选: '已选',
  项: '项',
  清除: '清除',
  查询无数据: '查询无数据',
  确定: '确定',
  取消: '取消',
  最近搜索: '最近搜索',
  今天: '今天',
  近7天: '近7天',
  近15天: '近15天',
  近30天: '近30天',
  表格设置: '表格设置',
  字段显示设置: '字段显示设置',
  '（最多{max}项）': '（最多{max}项）',
  全选: '全选',
  表格行高: '表格行高',
  小: '小',
  中: '中',
  大: '大',
  默认排序表头设置: '默认排序表头设置',
  '（请在下面可排序字段中选择）': '（请在下面可排序字段中选择）',
  '（': '（',
  默认: '默认',
  升序: '升序',
  降序: '降序',
  切换: '切换',
  '）': '）',
  快捷键列表: '快捷键列表',
  放大: '放大',
  缩小: '缩小',
  还原: '还原',
  缩放: '缩放',
  鼠标滚动: '鼠标滚动',
  '连续选中（或取消）节点': '连续选中（或取消）节点',
  鼠标左键单击: '鼠标左键单击',
  移动流程元素: '移动流程元素',
  选中后: '选中后',
  '箭头（上下左右）': '箭头（上下左右）',
  删除节点: '删除节点',
  '复制/粘贴': '复制/粘贴',
  '最近 x 次成功执行耗时': '最近 {num} 次成功执行耗时',
  已运行: '已运行',
  暂无成功执行历史: '暂无成功执行历史',
  变量引用: '变量引用',
  跳过: '跳过',
  结束: '结束',
  复制节点: '复制节点',
  复制并插入: '复制并插入',
  解除连线: '解除连线',
  删除连线: '删除连线',
  标准插件节点: '标准插件节点',
  子流程节点: '子流程节点',
  并行网关: '并行网关',
  分支网关: '分支网关',
  汇聚网关: '汇聚网关',
  条件并行网关: '条件并行网关',
  开始: '开始',
  重试: '重试',
  暂停: '暂停',
  强制终止: '强制终止',
  继续: '继续',
  节点参数: '节点参数',
  审批: '审批',
  '当前插件即将停止维护，请更新插件版本': '当前插件即将停止维护，请更新插件版本',
  '当前插件已停止维护，请更新插件版本': '当前插件已停止维护，请更新插件版本',
  '标准插件节点：': '标准插件节点：',
  '已封装好的可用插件，可直接选中拖拽至画布中。': '已封装好的可用插件，可直接选中拖拽至画布中。',
  '子流程：': '子流程：',
  '同一个项目下已新建的流程，作为子流程可以嵌套进至当前流程，并在执行任务时可以操作子流程的单个节点。': '同一个项目下已新建的流程，作为子流程可以嵌套进至当前流程，并在执行任务时可以操作子流程的单个节点。',
  '并行网关：': '并行网关：',
  '有多个流出分支，并且多个流出分支都默认执行。': '有多个流出分支，并且多个流出分支都默认执行。',
  '分支网关：': '分支网关：',
  '执行符合条件的唯一流出分支。': '执行符合条件的唯一流出分支。',
  '汇聚网关：': '汇聚网关：',
  '当汇聚网关用于汇聚并行网关时，所有进入顺序流的分支都到达以后，流程才会通过汇聚网关。': '当汇聚网关用于汇聚并行网关时，所有进入顺序流的分支都到达以后，流程才会通过汇聚网关。',
  '条件并行网关：': '条件并行网关：',
  '执行时满足分支条件的都会执行。': '执行时满足分支条件的都会执行。',
  请选择标签: '请选择标签',
  请输入插件名称: '请输入插件名称',
  搜索结果为空: '搜索结果为空',
  搜索插件: '搜索插件',
  内置插件: '内置插件',
  第三方插件: '第三方插件',
  API插件: 'API插件',
  项目流程: '项目流程',
  公共流程: '公共流程',
  节点: '节点',
  请输入流程名称: '请输入流程名称',
  缩略视图: '缩略视图',
  复位: '复位',
  节点框选: '节点框选',
  排版: '排版',
  快捷键: '快捷键',
  变量引用预览: '变量引用预览',
  反选: '反选',
  请输入: '请输入',
  条件: '条件',
  值: '值',
  查看配置: '查看配置',
  内容填写不完整: '内容填写不完整',
  组合条件: '组合条件',
  序号: '序号',
  '查看字段/列': '查看字段/列',
  '删除字段/列': '删除字段/列',
  '确定删除字段/列吗？': '确定删除字段/列吗？',
  '删除时，会将整列内容同时删除且不可撤回，请谨慎！': '删除时，会将整列内容同时删除且不可撤回，请谨慎！',
  删除: '删除',
  添加字段: '添加字段',
  条件字段: '条件字段',
  结果字段: '结果字段',
  暂未添加条件字段: '暂未添加条件字段',
  '条件字段未配置，请先完成字段配置': '条件字段未配置，请先完成字段配置',
  暂未添加结果字段: '暂未添加结果字段',
  '结果字段未配置，请先完成字段配置': '结果字段未配置，请先完成字段配置',
  请至少完整填写一条规则: '请至少完整填写一条规则',
  新增: '新增',
  编辑: '编辑',
  查看: '查看',
  且: '且',
  或: '或',
  字段: '字段',
  新增条件组: '新增条件组',
  规则设置方式: '规则设置方式',
  表单设置: '表单设置',
  表达式: '表达式',
  '请填入需要的 FEEL 表达式，支持的语法可参考': '请填入需要的 FEEL 表达式，支持的语法可参考',
  文档: '文档',
  选项名称: '选项名称',
  选项值: '选项值',
  添加选项: '添加选项',
  选项: '选项',
  风险提示: '风险提示',
  '修改或删除选项可能导致现有规则不适配，请谨慎操作': '修改或删除选项可能导致现有规则不适配，请谨慎操作',
  保存: '保存',
  请选择空间: '请选择空间',
  新建空间: '新建空间',
  空间管理: '空间管理',
  系统管理: '系统管理',
  中文: '中文',
  产品文档: '产品文档',
  版本日志: '版本日志',
  流程: '流程',
  任务: '任务',
  调试任务: '调试任务',
  决策表: '决策表',
  空间配置: '空间配置',
  模块配置: '模块配置',
  当前版本: '当前版本',
  开始节点: '开始节点',
  结束节点: '结束节点',
  任务节点: '任务节点',
  汇聚节点: '汇聚节点',
  重试子流程: '重试子流程',
  跳过子流程: '跳过子流程',
  导出: '导出',
  提示: '提示',
  已过期: '已过期',
  未执行: '未执行',
  执行中: '执行中',
  排队中: '排队中',
  节点暂停: '节点暂停',
  失败: '失败',
  完成: '完成',
  终止: '终止',
  运维工具: '运维工具',
  监控告警: '监控告警',
  配置管理: '配置管理',
  开发工具: '开发工具',
  企业IT: '企业IT',
  办公应用: '办公应用',
  其它: '其它',
  默认分类: '默认分类',
  手动: '手动',
  API网关: 'API网关',
  轻应用: '轻应用',
  周期任务: '周期任务',
  移动端: '移动端',
  计划任务: '计划任务',
  '输入定时表达式非法，请校验': '输入定时表达式非法，请校验',
  间隔时间必须是正整数: '间隔时间必须是正整数',
  流程名称不能为空: '流程名称不能为空',
  流程名称不能包含: '流程名称不能包含',
  非法字符: '非法字符',
  流程名称长度不能超过: '流程名称长度不能超过',
  个字符: '个字符',
  节点名称不能为空: '节点名称不能为空',
  节点名称不能包含: '节点名称不能包含',
  节点名称长度不能超过: '节点名称长度不能超过',
  步骤名称不能包含: '步骤名称不能包含',
  步骤名称长度不能超过: '步骤名称长度不能超过',
  变量名称不能为空: '变量名称不能为空',
  变量名称不能包含: '变量名称不能包含',
  变量名称长度不能超过: '变量名称长度不能超过',
  变量KEY值不能为空: '变量KEY值不能为空',
  '变量KEY由英文字母、数字、下划线组成，不允许使用系统变量及业务环境变量命名规则，且不能以数字开头': '变量KEY由英文字母、数字、下划线组成，不允许使用系统变量及业务环境变量命名规则，且不能以数字开头',
  变量KEY值长度不能超过: '变量KEY值长度不能超过',
  变量KEY值已存在: '变量KEY值已存在',
  变量隐藏时默认值不能为空: '变量隐藏时默认值不能为空',
  默认值不满足正则校验: '默认值不满足正则校验',
  正则校验不是合法的正则表达式: '正则校验不是合法的正则表达式',
  任务名称不能为空: '任务名称不能为空',
  任务名称不能包含: '任务名称不能包含',
  任务名称不能超过: '任务名称不能超过',
  方案名称不能为空: '方案名称不能为空',
  方案名称不能包含: '方案名称不能包含',
  方案名称不能超过: '方案名称不能超过',
  流程模板不能为空: '流程模板不能为空',
  应用名称不能为空: '应用名称不能为空',
  应用名称不能包含: '应用名称不能包含',
  应用名称不能超过: '应用名称不能超过',
  应用简介不能超过: '应用简介不能超过',
  定时流程名称不能为空: '定时流程名称不能为空',
  定时流程名称包含: '定时流程名称包含',
  定时流程名称不能超过: '定时流程名称不能超过',
  项目名称不能为空: '项目名称不能为空',
  项目名称包含: '项目名称包含',
  项目名称不能超过: '项目名称不能超过',
  项目描述不能超过: '项目描述不能超过',
  定时表达式不能为空: '定时表达式不能为空',
  间隔时间不能为空: '间隔时间不能为空',
  本地缓存名称不能为空: '本地缓存名称不能为空',
  本地缓存名称包含: '本地缓存名称包含',
  本地缓存名称不能超过: '本地缓存名称不能超过',
  '名称由英文字母、数字、下划线组成，且不能以数字开头': '名称由英文字母、数字、下划线组成，且不能以数字开头',
  名称长度不能超过: '名称长度不能超过',
  开始分钟数不能为空: '开始分钟数不能为空',
  '请输入 0 - 59 之间的数': '请输入 0 - 59 之间的数',
  开始小时数不能为空: '开始小时数不能为空',
  '请输入 0 - 23 之间的数': '请输入 0 - 23 之间的数',
  开始周数不能为空: '开始周数不能为空',
  '请输入 0 - 6 之间的数': '请输入 0 - 6 之间的数',
  开始天数不能为空: '开始天数不能为空',
  '请输入 1 - 31 之间的数': '请输入 1 - 31 之间的数',
  开始月数不能为空: '开始月数不能为空',
  '请输入 1 - 12 之间的数': '请输入 1 - 12 之间的数',
  test不能为空: 'test不能为空',
  '请输入 test 之间的数': '请输入 test 之间的数',
  已复制: '已复制',
  '确认离开当前页?': '确认离开当前页?',
  离开将会导致未保存信息丢失: '离开将会导致未保存信息丢失',
  离开: '离开',
  蓝鲸流程引擎平台: '蓝鲸流程引擎平台',
  蓝鲸智云: '蓝鲸智云',
  展开详情: '展开详情',
  隐藏详情: '隐藏详情',
  复制成功: '复制成功',
  小于: '小于',
  秒: '秒',
  节点不可连接自身: '节点不可连接自身',
  只能添加输入连线: '只能添加输入连线',
  只能添加输出连线: '只能添加输出连线',
  不能连接: '不能连接',
  相同节点不能回连: '相同节点不能回连',
  重复添加连线: '重复添加连线',
  已达到: '已达到',
  最大输出连线条数: '最大输出连线条数',
  最大输入连线条数: '最大输入连线条数',
  在模板中只能添加一个: '在模板中只能添加一个',
  至少需要: '至少需要',
  条输入连线: '条输入连线',
  条输出连线: '条输出连线',
  请添加任务节点: '请添加任务节点',
  并行网关缺少对应的汇聚网关: '并行网关缺少对应的汇聚网关',
  条件并行网关缺少对应的汇聚网关: '条件并行网关缺少对应的汇聚网关',
  '没找到页面！': '没找到页面！',
  编辑空间配置: '编辑空间配置',
  新增空间配置: '新增空间配置',
  提交: '提交',
  空间名称: '空间名称',
  '仅支持您是开发者的 app_code': '仅支持您是开发者的 app_code',
  空间描述: '空间描述',
  平台提供服务的地址: '平台提供服务的地址',
  '请提供以 https:// 或 http:// 开头的服务地址': '请提供以 https:// 或 http:// 开头的服务地址',
  '修改成功！': '修改成功！',
  '新增成功！': '新增成功！',
  创建时间: '创建时间',
  更新时间: '更新时间',
  开始时间: '开始时间',
  结束时间: '结束时间',
  调试: '调试',
  创建任务: '创建任务',
  字段输入: '字段输入',
  输出结果: '输出结果',
  关闭: '关闭',
  '确认删除该决策表？': '确认删除该决策表？',
  '决策表名称：': '决策表名称：',
  '删除后不可恢复，请谨慎操作！': '删除后不可恢复，请谨慎操作！',
  '当前决策表已被流程使用，禁止删除！': '当前决策表已被流程使用，禁止删除！',
  '决策表删除成功！': '决策表删除成功！',
  编辑决策表: '编辑决策表',
  新建决策表: '新建决策表',
  基础信息: '基础信息',
  名称: '名称',
  关联流程: '关联流程',
  描述: '描述',
  规则配置: '规则配置',
  需保证有且仅有一条规则被命中: '需保证有且仅有一条规则被命中',
  '该决策表已被流程中的节点引用，仅支持修改规则，不支持修改字段(修改字段需要修改节点引用后重新保存流程)。': '该决策表已被流程中的节点引用，仅支持修改规则，不支持修改字段(修改字段需要修改节点引用后重新保存流程)。',
  '{0}成功': '{0}成功',
  新建: '新建',
  '决策表{0}成功': '决策表{0}成功',
  可以回到流程里继续使用: '可以回到流程里继续使用',
  关闭当前页: '关闭当前页',
  '确定保存决策表并去执行调试?': '确定保存决策表并去执行调试?',
  决策表详情: '决策表详情',
  '名称：': '名称：',
  '描述：': '描述：',
  '关联流程：': '关联流程：',
  请选择: '请选择',
  'ID/名称/创建人/更新人/所属模板 ID/所属作用域类型/所属作用域值': 'ID/名称/创建人/更新人/所属模板 ID/所属作用域类型/所属作用域值',
  操作: '操作',
  所属作用域类型: '所属作用域类型',
  所属作用域值: '所属作用域值',
  '所属模板 ID': '所属模板 ID',
  创建人: '创建人',
  更新人: '更新人',
  恢复默认值: '恢复默认值',
  编辑配置: '编辑配置',
  说明: '说明',
  '数据格式不正确，应为JSON格式': '数据格式不正确，应为JSON格式',
  '确认" {0} "恢复默认值?': '确认" {0} "恢复默认值?',
  '删除成功！': '删除成功！',
  'ID/任务名称/创建人/执行人/所属模板 ID/所属作用域类型/所属作用域值': 'ID/任务名称/创建人/执行人/所属模板 ID/所属作用域类型/所属作用域值',
  引擎操作: '引擎操作',
  任务名称: '任务名称',
  执行人: '执行人',
  状态: '状态',
  '引擎实例 Id': '引擎实例 Id',
  已暂停: '已暂停',
  未知: '未知',
  '任务删除成功！': '任务删除成功！',
  新建任务: '新建任务',
  模板ID: '模板ID',
  请求参数: '请求参数',
  '请求参数格式不正确，应为JSON格式': '请求参数格式不正确，应为JSON格式',
  任务创建成功: '任务创建成功',
  新建流程: '新建流程',
  流程名称: '流程名称',
  流程创建成功: '流程创建成功',
  'ID/流程名称/创建人/更新人/启用/所属作用域类型/所属作用域值': 'ID/流程名称/创建人/更新人/启用/所属作用域类型/所属作用域值',
  创建流程: '创建流程',
  '当前已选择 x 条数据': '当前已选择 {num} 条数据',
  '，': '，',
  清除选择: '清除选择',
  启用: '启用',
  模板名称: '模板名称',
  本页全选: '本页全选',
  跨页全选: '跨页全选',
  '流程删除成功！': '流程删除成功！',
  '确认删除流程"{0}"?': '确认删除流程"{0}"?',
  编辑模块配置: '编辑模块配置',
  新增模块配置: '新增模块配置',
  '确认删除模块" {0} " ?': '确认删除模块" {0} " ?',
  请求: '请求',
  引擎版本: '引擎版本',
  请求资源: '请求资源',
  路径参数: '路径参数',
  '对应接口参数及参数类型请参考【': '对应接口参数及参数类型请参考【',
  '】': '】',
  请完善请求信息: '请完善请求信息',
  发送请求: '发送请求',
  重置: '重置',
  响应: '响应',
  请先发送请求: '请先发送请求',
  请输入完整的路径参数: '请输入完整的路径参数',
  'Body格式不正确，应为JSON格式': 'Body格式不正确，应为JSON格式',
  键: '键',
  执行记录: '执行记录',
  配置快照: '配置快照',
  操作历史: '操作历史',
  调用日志: '调用日志',
  第: '第',
  次循环: '次循环',
  次执行: '次执行',
  继续执行: '继续执行',
  失败后跳过: '失败后跳过',
  '默认值不符合正则规则：': '默认值不符合正则规则：',
  流程模板: '流程模板',
  标准插件: '标准插件',
  插件版本: '插件版本',
  节点名称: '节点名称',
  步骤名称: '步骤名称',
  执行方案: '执行方案',
  是否可选: '是否可选',
  是: '是',
  否: '否',
  失败处理: '失败处理',
  自动跳过: '自动跳过',
  手动跳过: '手动跳过',
  手动重试: '手动重试',
  在: '在',
  后: '后',
  自动重试: '自动重试',
  次: '次',
  超时控制: '超时控制',
  总是使用最新版本: '总是使用最新版本',
  输入参数: '输入参数',
  暂无参数: '暂无参数',
  输出参数: '输出参数',
  强制终止后跳过: '强制终止后跳过',
  超时: '超时',
  则: '则',
  节点执行记录: '节点执行记录',
  异常信息: '异常信息',
  任务名: '任务名',
  耗时: '耗时',
  详情: '详情',
  显示全部: '显示全部',
  暂无异常: '暂无异常',
  执行信息: '执行信息',
  子流程详情: '子流程详情',
  暂无执行信息: '暂无执行信息',
  参数名: '参数名',
  参数值: '参数值',
  平台日志: '平台日志',
  第三方插件日志: '第三方插件日志',
  暂无日志: '暂无日志',
  操作时间: '操作时间',
  操作类型: '操作类型',
  操作来源: '操作来源',
  操作人: '操作人',
  暂无输出: '暂无输出',
  参数Key: '参数Key',
  请选择执行分支: '请选择执行分支',
  可选执行分支: '可选执行分支',
  注入全局变量: '注入全局变量',
  '请输入变量的KEY,如${KEY}': '请输入变量的KEY,如${KEY}',
  请输入变量的值: '请输入变量的值',
  请选择变量值的类型: '请选择变量值的类型',
  字符串: '字符串',
  整数: '整数',
  字典: '字典',
  变量Value值不能为空: '变量Value值不能为空',
  变量Value格式不正确: '变量Value格式不正确',
  至少保留一条全局变量: '至少保留一条全局变量',
  保存并继续: '保存并继续',
  去修改: '去修改',
  暂停去修改: '暂停去修改',
  '参数已被使用，不可修改': '参数已被使用，不可修改',
  任务已暂停执行: '任务已暂停执行',
  修改入参: '修改入参',
  参数未修改: '参数未修改',
  '参数未修改，任务已继续执行': '参数未修改，任务已继续执行',
  '保存失败，有参数已被使用不可修改': '保存失败，有参数已被使用不可修改',
  参数修改成功: '参数修改成功',
  '参数修改成功，任务已继续执行': '参数修改成功，任务已继续执行',
  修改成功: '修改成功',
  '退回节点：': '退回节点：',
  来源: '来源',
  参数明细: '参数明细',
  分支类型: '分支类型',
  自定义分支: '自定义分支',
  默认分支: '默认分支',
  feelOperates: '支持 "=、!=、>、>=、<、<=" 等二元比较操作符',
  '支持 "and、or、true、false" 等关键字语法': '支持 "and、or、true、false" 等关键字语法',
  '支持 FEEL (Friendly Enough Expression Language) 基础语法': '支持 FEEL (Friendly Enough Expression Language) 基础语法',
  '支持使用全局变量，如': '支持使用全局变量，如',
  '${}中支持使用内置函数、datetime、re、hashlib、random、time、os.path模块处理全局变量': '${}中支持使用内置函数、datetime、re、hashlib、random、time、os.path模块处理全局变量',
  '因 MAKO 语法限制：请勿使用 \'$\', \'{\' 和 \'}\' 字符': '因 MAKO 语法限制：请勿使用 \'$\', \'{\' 和 \'}\' 字符',
  '示例：': '示例：',
  '字符串比较：': '字符串比较：',
  '数值比较：': '数值比较：',
  '包含：': '包含：',
  makoOperates: '支持 "==、!=、>、>=、<、<=、in、notin" 等二元比较操作符',
  '支持 "and、or、True/true、False/false" 等关键字语法': '支持 "and、or、True/true、False/false" 等关键字语法',
  表达式更多细节请参考: '表达式更多细节请参考',
  '支持使用内置函数、datetime、re、hashlib、random、time、os.path模块处理全局变量': '支持使用内置函数、datetime、re、hashlib、random、time、os.path模块处理全局变量',
  '假设 key 是值为 "3" 的全局变量': '假设 key 是值为 "3" 的全局变量',
  '使用内置函数、datetime、re、hashlib、random、time、os.path模块处理全局变量': '使用内置函数、datetime、re、hashlib、random、time、os.path模块处理全局变量',
  支持直接引用全局变量: '支持直接引用全局变量',
  '支持 "and、or、not、True、False" 等 MAKO 关键字语法': '支持 "and、or、not、True、False" 等 MAKO 关键字语法',
  '所有分支均不匹配时执行，类似switch-case-default里面的default': '所有分支均不匹配时执行，类似switch-case-default里面的default',
  任务执行信息: '任务执行信息',
  '存在子流程节点执行失败，可从节点执行记录去往子任务处理，并及时': '存在子流程节点执行失败，可从节点执行记录去往子任务处理，并及时',
  刷新任务状态: '刷新任务状态',
  '。': '。',
  审批意见: '审批意见',
  通过: '通过',
  拒绝: '拒绝',
  备注: '备注',
  执行: '执行',
  任务开始执行: '任务开始执行',
  任务已继续执行: '任务已继续执行',
  任务终止成功: '任务终止成功',
  跳过成功: '跳过成功',
  强制终止执行成功: '强制终止执行成功',
  继续成功: '继续成功',
  '确定重试当前节点？': '确定重试当前节点？',
  '重新节点将重新执行当前节点（使用最新的参数值）': '重新节点将重新执行当前节点（使用最新的参数值）',
  重试成功: '重试成功',
  '确定跳过当前节点?': '确定跳过当前节点?',
  跳过节点将忽略当前失败节点继续往后执行: '跳过节点将忽略当前失败节点继续往后执行',
  '确定强制终止当前节点?': '确定强制终止当前节点?',
  '强制终止将强行修改节点状态为失败，但不会中断已经发送到其它系统的请求': '强制终止将强行修改节点状态为失败，但不会中断已经发送到其它系统的请求',
  修改时间: '修改时间',
  '确定继续往后执行?': '确定继续往后执行?',
  并行: '并行',
  '确定终止当前任务?': '确定终止当前任务?',
  '终止任务将停止执行任务，但执行中节点将运行完成': '终止任务将停止执行任务，但执行中节点将运行完成',
  节点详情: '节点详情',
  注入全局变量成功: '注入全局变量成功',
  任务执行: '任务执行',
  查看流程: '查看流程',
  任务入参: '任务入参',
  查看节点详情: '查看节点详情',
  操作记录: '操作记录',
  跳转任务列表: '跳转任务列表',
  全局变量: '全局变量',
  '设为「常量」的参数中途不允许修改': '设为「常量」的参数中途不允许修改',
  '参数值不符合正则规则：': '参数值不符合正则规则：',
  批量更新子流程: '批量更新子流程',
  '子流程更新时，如果新旧版本存在相同表单，表单数据会默认取原表单数据': '子流程更新时，如果新旧版本存在相同表单，表单数据会默认取原表单数据',
  原版本: '原版本',
  待更新版本: '待更新版本',
  已选择: '已选择',
  个: '个',
  待更新的子流程: '待更新的子流程',
  批量更新: '批量更新',
  '全局变量【 x 】的引用数已为 0。如果不再使用，可立即删除变量; 也可以稍后在全局变量面板中删除': '全局变量【 {key} 】的引用数已为 0。如果不再使用，可立即删除变量; 也可以稍后在全局变量面板中删除',
  删除变量: '删除变量',
  以后再说: '以后再说',
  头部区域: '头部区域',
  分支条件: '分支条件',
  分支名称: '分支名称',
  '表达式语法与当前空间下配置的解析语法 [ {0} ]不匹配，保存失败。请重新添加网关，已存在的网关仍按原有语法解析。': '表达式语法与当前空间下配置的解析语法 [ {0} ]不匹配，保存失败。请重新添加网关，已存在的网关仍按原有语法解析。',
  '确定保存修改的内容？': '确定保存修改的内容？',
  当前流程模板在浏览器多个标签页打开: '当前流程模板在浏览器多个标签页打开',
  双击左键: '双击左键',
  可以快捷打开节点配置面板: '可以快捷打开节点配置面板',
  '确定保存流程并去设置执行方案？': '确定保存流程并去设置执行方案？',
  '确定保存克隆流程并去设置执行方案？': '确定保存克隆流程并去设置执行方案？',
  '自定义变量中存在系统变量/项目变量的key，需要清除后才能保存，是否一键清除？(可通过【模版数据-constants】进行确认)': '自定义变量中存在系统变量/项目变量的key，需要清除后才能保存，是否一键清除？(可通过【模版数据-constants】进行确认)',
  '问题变量有：': '问题变量有：',
  保存成功: '保存成功',
  '任务节点参数错误，请点击错误节点查看详情': '任务节点参数错误，请点击错误节点查看详情',
  请选择节点的插件类型: '请选择节点的插件类型',
  请选择节点的子流程: '请选择节点的子流程',
  '表单值中不存在 {0} 属性': '表单值中不存在 {0} 属性',
  '排版完成，原内容在本地快照中': '排版完成，原内容在本地快照中',
  '确定保存流程并去调试?': '确定保存流程并去调试?',
  方案保存成功: '方案保存成功',
  新增流程本地快照成功: '新增流程本地快照成功',
  '最近快照已保存，并恢复至原序号为': '最近快照已保存，并恢复至原序号为',
  的快照: '的快照',
  替换流程成功: '替换流程成功',
  自动保存: '自动保存',
  '系统不会保存您所做的更改，确认离开？': '系统不会保存您所做的更改，确认离开？',
  重选: '重选',
  选择: '选择',
  节点标签: '节点标签',
  '未选择失败处理方式，标准插件节点如果执行失败，会导致任务中断后不可继续': '未选择失败处理方式，标准插件节点如果执行失败，会导致任务中断后不可继续',
  '自动跳过：标准插件节点如果执行失败，会自动忽略错误并把节点状态设置为成功。': '自动跳过：标准插件节点如果执行失败，会自动忽略错误并把节点状态设置为成功。',
  '手动跳过：标准插件节点如果执行失败，可以人工干预，直接跳过节点的执行。': '手动跳过：标准插件节点如果执行失败，可以人工干预，直接跳过节点的执行。',
  '手动重试：标准插件节点如果执行失败，可以人工干预，填写参数后重试节点。': '手动重试：标准插件节点如果执行失败，可以人工干预，填写参数后重试节点。',
  '自动重试：标准插件节点如果执行失败，系统会自动以原参数进行重试。': '自动重试：标准插件节点如果执行失败，系统会自动以原参数进行重试。',
  执行代理人: '执行代理人',
  请输入用户: '请输入用户',
  '子流程有更新，更新时若存在相同表单数据则获取原表单的值。': '子流程有更新，更新时若存在相同表单数据则获取原表单的值。',
  更新子流程: '更新子流程',
  每次创建任务会使用选中执行方案的最新版本且不会提示该节点需要更新: '每次创建任务会使用选中执行方案的最新版本且不会提示该节点需要更新',
  '此流程无执行方案，无需选择': '此流程无执行方案，无需选择',
  该功能仅对V2引擎生效: '该功能仅对V2引擎生效',
  '打开此功能后，每次创建任务会尝试使用子流程的最新版本，并且不会再提示该节点需要更新。': '打开此功能后，每次创建任务会尝试使用子流程的最新版本，并且不会再提示该节点需要更新。',
  '若子流程中发生变动，标准运维会采用以下处理策略，如处理不符合预期，请谨慎使用。': '若子流程中发生变动，标准运维会采用以下处理策略，如处理不符合预期，请谨慎使用。',
  请选择插件: '请选择插件',
  请选择插件版本: '请选择插件版本',
  请选择流程模板: '请选择流程模板',
  不使用执行方案: '不使用执行方案',
  '无法获取决策表配置数据，引用的决策表可能已被删除': '无法获取决策表配置数据，引用的决策表可能已被删除',
  '切换决策表输出参数将被清除，是否继续切换？': '切换决策表输出参数将被清除，是否继续切换？',
  '无匹配数据，可【回车】插入后新建变量': '无匹配数据，可【回车】插入后新建变量',
  取消变量快捷输入: '取消变量快捷输入',
  变量快捷输入: '变量快捷输入',
  查看未引用变量: '查看未引用变量',
  未找到可用的插件或插件版本: '未找到可用的插件或插件版本',
  重选插件: '重选插件',
  请选择子流程: '请选择子流程',
  json数据格式不正确: 'json数据格式不正确',
  勾选为全局变量: '勾选为全局变量',
  取消接收输出: '取消接收输出',
  使用变量接收输出: '使用变量接收输出',
  变量配置: '变量配置',
  '已存在相同KEY的变量，请新建变量': '已存在相同KEY的变量，请新建变量',
  创建方式: '创建方式',
  自动创建: '自动创建',
  变量复用: '变量复用',
  手动创建: '手动创建',
  复用变量: '复用变量',
  变量名称: '变量名称',
  变量KEY: '变量KEY',
  '变量KEY为特殊标志符变量，请修改': '变量KEY为特殊标志符变量，请修改',
  '找不到想要的插件？可以尝试自己动手开发！': '找不到想要的插件？可以尝试自己动手开发！',
  由: '由',
  提供: '提供',
  请选择流程进行节点配置: '请选择流程进行节点配置',
  标签: '标签',
  节点配置: '节点配置',
  新建变量: '新建变量',
  属性: '属性',
  输入: '输入',
  输出: '输出',
  显示: '显示',
  隐藏: '隐藏',
  选择操作: '选择操作',
  请填写完整参数: '请填写完整参数',
  生成并复制代码: '生成并复制代码',
  本地快照: '本地快照',
  '可自动保存最近的50次快照，每5分钟一次。仅在本地浏览器存储。': '可自动保存最近的50次快照，每5分钟一次。仅在本地浏览器存储。',
  查看需要更新的子流程: '查看需要更新的子流程',
  '建议及时处理子流程更新，涉及': '建议及时处理子流程更新，涉及',
  个子流程节点: '个子流程节点',
  编辑执行方案: '编辑执行方案',
  个节点: '个节点',
  导出图片: '导出图片',
  关闭预览: '关闭预览',
  创建任务: '创建任务',
  编辑流程: '编辑流程',
  保存并新建任务: '保存并新建任务',
  项目: '项目',
  不允许添加没有节点的执行方案: '不允许添加没有节点的执行方案',
  成功: '成功',
  通知人: '通知人',
  请选择通知人: '请选择通知人',
  通知方式: '通知方式',
  通知分组: '通知分组',
  管理项目变量: '管理项目变量',
  变量快捷处理: '变量快捷处理',
  '属性：': '属性：',
  '"来源/是否显示"格式，来源是输入类型': '"来源/是否显示"格式，来源是输入类型',
  '表示变量来自用户添加的变量或者标准插件/子流程节点输入参数引用的变量，来源是输出类型': '表示变量来自用户添加的变量或者标准插件/子流程节点输入参数引用的变量，来源是输出类型',
  '表示变量来自标准插件/子流程节点输出参数引用的变量；是否显示表示该变量在新建任务填写参数时是否展示给用户，': '表示变量来自标准插件/子流程节点输出参数引用的变量；是否显示表示该变量在新建任务填写参数时是否展示给用户，',
  '表示显示，': '表示显示，',
  '表示隐藏，输出类型的变量一定是隐藏的。': '表示隐藏，输出类型的变量一定是隐藏的。',
  '输出：': '输出：',
  '表示该变量会作为该流程模板的输出参数，在被其他流程模板当做子流程节点时可以引用。': '表示该变量会作为该流程模板的输出参数，在被其他流程模板当做子流程节点时可以引用。',
  '模板预渲染：': '模板预渲染：',
  '模板预渲染为“是”时，任务会在执行前将变量中的 MAKO 段进行渲染，': '模板预渲染为“是”时，任务会在执行前将变量中的 MAKO 段进行渲染，',
  '而不是在第一个引用该变量的节点执行前才进行渲染；': '而不是在第一个引用该变量的节点执行前才进行渲染；',
  '如果需要预渲染的变量引用了别的变量，': '如果需要预渲染的变量引用了别的变量，',
  '那么被引用变量的预渲染也要设置为“是”，否则预渲染不生效。': '那么被引用变量的预渲染也要设置为“是”，否则预渲染不生效。',
  跨流程克隆: '跨流程克隆',
  已选择x项: '已选择{num}项',
  隐藏系统变量: '隐藏系统变量',
  引用: '引用',
  '直接引用全局变量的节点数量，点击数字查看引用详情': '直接引用全局变量的节点数量，点击数字查看引用详情',
  '显示（入参）': '显示（入参）',
  '无数据，请手动新增变量或者勾选标准插件参数自动生成': '无数据，请手动新增变量或者勾选标准插件参数自动生成',
  系统变量: '系统变量',
  项目变量: '项目变量',
  节点输出: '节点输出',
  节点输入: '节点输入',
  全部: '全部',
  变量: '变量',
  确认删除: '确认删除',
  '确认删除所选的x个变量?': '确认删除所选的{num}个变量?',
  '若该变量被节点引用，请及时检查并更新节点配置': '若该变量被节点引用，请及时检查并更新节点配置',
  引用变量的: '引用变量的',
  请输入关键字: '请输入关键字',
  申请权限: '申请权限',
  请选择流程模版: '请选择流程模版',
  '：': '：',
  变量信息: '变量信息',
  '无法克隆此变量，因克隆后变量长度超限': '无法克隆此变量，因克隆后变量长度超限',
  该流程暂无自定义全局变量: '该流程暂无自定义全局变量',
  全选所有变量: '全选所有变量',
  '已选择 x 个变量': '已选择 {num} 个变量',
  下一步: '下一步',
  上一步: '上一步',
  选择流程: '选择流程',
  选择变量: '选择变量',
  全部分类: '全部分类',
  '变量克隆成功！': '变量克隆成功！',
  正则校验: '正则校验',
  '配置为「显示」可在执行时做为任务入参使用，配置为「隐藏」则仅能在流程内部使用': '配置为「显示」可在执行时做为任务入参使用，配置为「隐藏」则仅能在流程内部使用',
  执行时显示: '执行时显示',
  '当满足条件时，原本做为入参的变量会隐藏起来无需录入': '当满足条件时，原本做为入参的变量会隐藏起来无需录入',
  '“显示参数”条件隐藏': '“显示参数”条件隐藏',
  开启: '开启',
  '所有变量值都会以字符串类型进行记录和判断，会忽略类型差异': '所有变量值都会以字符串类型进行记录和判断，会忽略类型差异',
  触发条件: '触发条件',
  '注意：如果命中条件，变量会保留填参页面的输入值并隐藏。如果变量为表单必填参数且输入值为空，可能会导致任务执行失败': '注意：如果命中条件，变量会保留填参页面的输入值并隐藏。如果变量为表单必填参数且输入值为空，可能会导致任务执行失败',
  '常量在任务启动就完成变量值的计算，使用变量时不再重新计算保持值不变': '常量在任务启动就完成变量值的计算，使用变量时不再重新计算保持值不变',
  常量: '常量',
  配置: '配置',
  默认值: '默认值',
  返回: '返回',
  '隐藏（非入参）': '隐藏（非入参）',
  即将下线: '即将下线',
  已下线: '已下线',
  组件: '组件',
  变量未找到: '变量未找到',
  关系组内的数据不能为空: '关系组内的数据不能为空',
  普通变量: '普通变量',
  动态变量: '动态变量',
  元变量: '元变量',
  默认值不符合正则规则: '默认值不符合正则规则',
  至少保留一条触发条件: '至少保留一条触发条件',
  点击可快速定位原节点: '点击可快速定位原节点',
  克隆: '克隆',
  操作名称: '操作名称',
  基础: '基础',
  请输入流程模板名称: '请输入流程模板名称',
  新建标签: '新建标签',
  标签管理: '标签管理',
  '模板分类即将下线，建议使用标签': '模板分类即将下线，建议使用标签',
  通知: '通知',
  '选择通知方式后，将默认通知到任务执行人': '选择通知方式后，将默认通知到任务执行人',
  任务状态: '任务状态',
  其他: '其他',
  推荐留空使用: '推荐留空使用',
  项目执行代理人设置: '项目执行代理人设置',
  '以便统一管理，也可单独配置流程执行代理人覆盖项目的设置': '以便统一管理，也可单独配置流程执行代理人覆盖项目的设置',
  请输入流程模板备注信息: '请输入流程模板备注信息',
  任务类型偏好: '任务类型偏好',
  默认任务: '默认任务',
  职能化任务: '职能化任务',
  标签名称: '标签名称',
  标签颜色: '标签颜色',
  选择颜色: '选择颜色',
  标签描述: '标签描述',
  标签名称不能超过: '标签名称不能超过',
  '标签已存在，请重新输入': '标签已存在，请重新输入',
  标签新建成功: '标签新建成功',
  '项目执行代理人(n)；免代理用户(m)': '项目执行代理人({n})；免代理用户({m})',
  保存时间: '保存时间',
  '无数据，请手动添加快照或等待自动保存': '无数据，请手动添加快照或等待自动保存',
  流程调试: '流程调试',
  调试执行: '调试执行',
  退出调试: '退出调试',
  执行调试: '执行调试',
  '自动载入上次调试选择的节点？': '自动载入上次调试选择的节点？',
  载入: '载入',
  请至少选择一个节点: '请至少选择一个节点',
  '确定保存Mock数据并去执行调试?': '确定保存Mock数据并去执行调试?',
  模板mock数据保存成功: '模板mock数据保存成功',
  填写调试入参: '填写调试入参',
  '选择 Mock 数据': '选择 Mock 数据',
  '无需Mock，真执行': '无需Mock，真执行',
  '收起未设置 Mock数据 的节点': '收起未设置 Mock数据 的节点',
  '显示未设置 Mock数据 的节点': '显示未设置 Mock数据 的节点',
  '后，则': '后，则',
  '，间隔': '，间隔',
  '你可以设置多份 Mock 数据方案，用于不同调试场景。': '你可以设置多份 Mock 数据方案，用于不同调试场景。',
  新增数据方案: '新增数据方案',
  设为默认: '设为默认',
  暂无字段可设置: '暂无字段可设置',
  收起未勾选的字段: '收起未勾选的字段',
  显示未勾选的字段: '显示未勾选的字段',
  '暂无输出字段，Mock方案输出为空': '暂无输出字段，Mock方案输出为空',
  暂无方案: '暂无方案',
  立即新增: '立即新增',
  'Mock 数据方案': 'Mock 数据方案',
  '设置 Mock 数据': '设置 Mock 数据',
  Mock数据: 'Mock数据',
  插件配置: '插件配置',
  流程控制选项: '流程控制选项',
  '确认删除任务"{0}"?': '确认删除任务"{0}"?',
  '确认删除所选的 {0} 项流程吗?': '确认删除所选的 {0} 项流程吗?',
  等于: '等于',
  不等于: '不等于',
  包含: '包含',
  不包含: '不包含',
  大于: '大于',
  小于: '小于',
  大于等于: '大于等于',
  小于等于: '小于等于',
  在范围内: '在范围内',
  不在范围内: '不在范围内',
  编辑字段: '编辑字段',
  复制字段: '复制字段',
  向左插入1列: '向左插入1列',
  向右插入1列: '向右插入1列',
  复制行: '复制行',
  组合条件设置: '组合条件设置',
  向上插入1列: '向上插入1列',
  向下插入1列: '向下插入1列',
  删除行: '删除行',
  字段类型: '字段类型',
  文本: '文本',
  数值: '数值',
  单选下拉: '单选下拉',
  字段名称: '字段名称',
  字段标识: '字段标识',
  提示文字: '提示文字',
  描述信息: '描述信息',
  选项: '选项',
  projectPresentationTips: '一款 {0} 、 {1} 的流程引擎平台，助力系统快速获取 {2} 能力。',
  高效: '高效',
  灵活: '灵活',
  流程执行: '流程执行',
  立即体验: '立即体验',
  核心功能: '核心功能',
  流程编排和画布嵌入: '流程编排和画布嵌入',
  '直观地创建、编辑和管理流程，支持自定义权限控制。': '直观地创建、编辑和管理流程，支持自定义权限控制。',
  流程任务执行能力: '流程任务执行能力',
  '通过 API 实现流程任务的创建、执行和控制。': '通过 API 实现流程任务的创建、执行和控制。',
  决策引擎能力: '决策引擎能力',
  '可在流程中进行规则管理和决策。': '可在流程中进行规则管理和决策。',
  '便捷接入 & 集成': '便捷接入 & 集成',
  '多种接入方式：': '多种接入方式：',
  '支持不同的接入方式，满足不同系统的接入需求。': '支持不同的接入方式，满足不同系统的接入需求。',
  '多种集成方式：': '多种集成方式：',
  '支持 Web 服务集成和 SDK 集成，方便接入平台灵活集成。': '支持 Web 服务集成和 SDK 集成，方便接入平台灵活集成。',
  '流程引擎：': '流程引擎：',
  '决策引擎：': '决策引擎：',
  'FEEL 表达式解析器：': 'FEEL 表达式解析器：',
  '接入平台扩展 & 管理': '接入平台扩展 & 管理',
  '高度可扩展&自定义能力': '高度可扩展&自定义能力',
  '支持蓝鲸插件和 API 插件，': '支持蓝鲸插件和 API 插件，',
  '满足各种接入平台业务场景上的自定义需求。': '满足各种接入平台业务场景上的自定义需求。',
  数据管理能力: '数据管理能力',
  '提供空间数据管理端，': '提供空间数据管理端，',
  '接入平台管理员可基于空间视图进行资源数据管理和操作。': '接入平台管理员可基于空间视图进行资源数据管理和操作。',
  '支持 Webhook 订阅机制': '支持 Webhook 订阅机制',
  '接入平台可以轻松感知，': '接入平台可以轻松感知',
  '流程任务事件并进行自动化扩展。': '流程任务事件并进行自动化扩展。',
  支持计算和存储资源隔离: '支持计算和存储资源隔离',
  '任务执行和数据可与其他接入空间隔离。': '任务执行和数据可与其他接入空间隔离。',
  退出登录: '退出登录',
  首页: '首页',
  字段标识已经存在: '字段标识已经存在',
  key值只能由数字和字母组成且不能以数字开头: 'key值只能由数字和字母组成且不能以数字开头',
  必填项不能为空: '必填项不能为空',
  请求成功: '请求成功',
  '请求异常（外部系统错误或非法操作）': '请求异常（外部系统错误或非法操作）',
  '请求异常（内部系统发生未知错误）': '请求异常（内部系统发生未知错误）',
  秒: '秒 | {n} 秒 | {n} 秒',
  分钟: '分钟 | {n} 分钟 | {n} 分钟',
  小时: '小时 | {n} 小时 | {n} 小时',
  天: '天 | {n} 天 | {n} 天',
  新建全局变量: '新建全局变量',
  error_handle_秒: '秒',
  流程的入参: '流程的入参',
  scopeType: '指对应资源在接入平台所属的作用域范围的类型。如该资源属于业务 1，则该字段的值可设为"project"。',
  scopeValue: '指对应资源在接入平台所属的作用域范围的值。如该资源属于业务1，则该字段的值可设为"1"。',
  名字: '名字',
  mockReuseTips: '部分变量、Mock 方案已发生变化，继续操作将{0}以下不匹配的数据：',
  忽略: '忽略',
  继续复用: '继续复用',
  '调试数据已发生变化，请确认是否继续复用': '调试数据已发生变化，请确认是否继续复用',
  当前调试任务: '当前调试任务',
  待复用调试任务: '待复用调试任务',
  复用成功: '复用成功',
  id或name不唯一: 'id或name不唯一',
  '暂不支持带有英文双引号(") 的输入值': '暂不支持带有英文双引号(") 的输入值',
  变量列表: '变量列表',
  被赋值变量: '被赋值变量',
  新建凭证: '新建凭证',
  编辑凭证: '编辑凭证',
  凭证管理: '凭证管理',
  内容: '内容',
  '凭证删除后不可恢复，确认删除？': '凭证删除后不可恢复，确认删除？',
  值类型: '值类型',
  表单模式: '表单模式',
  json模式: 'json模式',
  插件名称: '插件名称',
  管理员: '管理员',
  授权状态: '授权状态',
  使用范围: '使用范围',
  授权状态修改时间: '授权状态修改时间',
  授权状态修改人: '授权状态修改人',
  授权状态: '授权状态',
  我的插件: '我的插件',
  '搜索插件名称、code、授权状态、管理员、授权状态修改人': '搜索插件名称、code、授权状态、管理员、授权状态修改人',
  编辑使用范围: '编辑使用范围',
  未授权: '未授权',
  蓝鲸插件: '蓝鲸插件',
  授权: '授权',
  '确认授权？': '确认授权？',
  授权成功: '授权成功',
  '授权后，授权空间可以直接调用插件接口并执行插件功能。': '授权后，授权空间可以直接调用插件接口并执行插件功能。',
  '注意：空间下的执行人信息由 BKFlow 的对接系统管理并传入，BKFlow 仅进行信息传递，不保证执行人信息的真实性。请尽量保证插件仅适用于平台对接场景，勿把传递的执行人信息作为鉴权依据。': '注意：空间下的执行人信息由 BKFlow 的对接系统管理并传入，BKFlow 仅进行信息传递，不保证执行人信息的真实性。请尽量保证插件仅适用于平台对接场景，勿把传递的执行人信息作为鉴权依据。',
  '插件名称：': '插件名称：',
  已授权: '已授权',
  取消授权: '取消授权',
  '确认取消授权？': '确认取消授权？',
  '取消后，流程配置页面将不能再看到对应的插件信息。': '取消后，流程配置页面将不能再看到对应的插件信息。',
  '注意：对于已经配置在流程中的插件节点，BKFlow 仍然会触发调用。如果希望完全不提供给 BKFlow 调用，请直接在 PaaS 开发者中心取消对 BKFlow 的应用授权。': '注意：对于已经配置在流程中的插件节点，BKFlow 仍然会触发调用。如果希望完全不提供给 BKFlow 调用，请直接在 PaaS 开发者中心取消对 BKFlow 的应用授权。',
  取消授权成功: '取消授权成功',
  '是否复制该流程？': '是否复制该流程？',
  '注意：关联的 mock 数据不会同步复制，暂不支持复制带有决策表节点的流程': '注意：关联的 mock 数据不会同步复制，暂不支持复制带有决策表节点的流程',
  '流程复制成功！': '流程复制成功！',
  使用范围编辑成功: '使用范围编辑成功',
  '* (对所有空间公开)': '* (对所有空间公开)',
};

export default cn;
