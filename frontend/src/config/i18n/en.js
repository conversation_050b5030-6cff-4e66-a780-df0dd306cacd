const en = {
  '1. 若子流程中增加了新的变量，在未手动更新子流程版本的情况下，将使用新变量默认值。': '1. If new variables are added to the subprocess and the subprocess version is not manually updated, the default value of the new variables will be used.',
  '2. 若子流程中修改了变量的默认值，在未手动更新子流程版本的情况下，将继续使用修改前变量的原有值。': '2. If the default values of variables are modified in the subprocess and the subprocess version is not manually updated, the original values of the variables before modification will continue to be used.',
  接口数据返回为空: 'Interface data is empty',
  '接口异常，': 'Interface exception,',
  'HTTP状态码：': 'HTTP status code:',
  分类: 'Category',
  api列表: 'API List',
  '统一 API 列表搜索功能需要 API 提供方支持，如果提供方不支持，则列表过滤不生效': 'Unified API list search functionality requires support from API providers. If providers do not support it, list filtering will not be effective.',
  请求方法: 'Request Method',
  api参数: 'API Parameters',
  取消变量引用: 'Cancel Variable Reference',
  设置为变量: 'Set as Variable',
  必填项: 'Required',
  暂无数据: 'No Data',
  '可以尝试 调整关键词 或': 'You can try adjusting keywords or',
  清空筛选条件: 'Clear Filters',
  复制: 'Copy',
  退出: 'Exit',
  全屏: 'Full Screen',
  '按 Esc 即可退出全屏模式': 'Press Esc to exit full screen mode',
  分: 'Min',
  时: 'Hour',
  日: 'Day',
  月: 'Month',
  周: 'Week',
  '长度超过100个字符，请修改规则': 'Length exceeds 100 characters, please modify the rule',
  '使用了除“,-*/”以外的特殊字符，请修改规则': 'Special characters other than ",-*/" used, please modify the rule',
  以及当月: 'and this month',
  '下次：': 'Next:',
  选择生成: 'Choose to Generate',
  手动输入: 'Manual Input',
  清空: 'Clear',
  天: 'Day',
  '0 表示星期天，6 表示星期六': '0 represents Sunday, and 6 represents Saturday',
  分钟: 'Minute',
  小时: 'Hour',
  星期: 'Weekday',
  日期: 'Date',
  月份: 'Month',
  循环: 'Cycle',
  从第: 'From the',
  从星期: 'From Weekday',
  '开始，每隔': 'Start, every',
  执行一次: 'Execute once',
  指定: 'Specify',
  星期一: 'Monday',
  星期二: 'Tuesday',
  星期三: 'Wednesday',
  星期四: 'Thursday',
  星期五: 'Friday',
  星期六: 'Saturday',
  星期天: 'Sunday',
  访问异常: 'Access Exception',
  仅提供从已认证平台发起访问: 'Only access from authenticated platforms is provided.',
  不支持直接请求本系统页面: 'Direct requests to the system pages are not supported.',
  '如有接入需求，请联系管理员': 'If access is required, please contact the administrator.',
  找不到页面: 'Page Not Found',
  '对不起，您没有当前应用的管理员权限': 'Sorry, you do not have administrator rights for the current application.',
  '请尝试如下操作：': 'Please try the following actions:',
  '联系“管理员”为您添加管理员权限': 'Contact the "administrator" to add administrator rights for you.',
  应用出现异常: 'Application Error',
  '系统出现异常, 请记录下错误场景并与开发人员联系, 谢谢!': 'An error occurred in the system. Please record the error scenario and contact the developers. Thank you!',
  收起: 'Collapse',
  未能重用: 'Failed to Reuse',
  将沿用原参数值提交: 'The original parameter values will be used for submission.',
  知道了: 'Got it',
  取消变量免渲染: 'Cancel Variable Exemption from Rendering',
  变量免渲染: 'Variable Exemption from Rendering',
  展开全部: 'Expand All',
  类型: 'Type',
  '请输入IP，多个以逗号或者换行符隔开，在cmdb上唯一': 'Please enter the IP address(es), separated by commas or new lines. These must be unique in the CMDB.',
  '请输入业务拓扑（形如：业务A>集群B>模块C），多个逗号或换行符隔开，在cmdb上唯一': 'Please enter the business topology (e.g., Business A>Cluster B>Module C), separated by commas or new lines. These must be unique in the CMDB.',
  '请输入动态分组名称，多个以逗号或换行符隔开，在cmdb上唯一': 'Please enter the dynamic group name(s), separated by commas or new lines. These must be unique in the CMDB.',
  表单保存数据与最新的CMDB: 'The saved form data and the latest CMDB:',
  '配置存在差异，是否更新变量数据？': 'Configuration discrepancy detected, update variable data?',
  确认: 'Confirm',
  '变量保存数据与最新的CMDB集群配置存在差异，是否更新变量数据？': 'The saved variable data differs from the latest CMDB cluster configuration, update variable data?',
  请选择日期时间: 'Please Select Date & Time',
  禁用选择器: 'Disable Selector',
  已选: 'Selected',
  项: 'Items',
  清除: 'Clear',
  查询无数据: 'No Data Found',
  确定: 'OK',
  取消: 'Cancel',
  最近搜索: 'Recent Searches',
  今天: 'Today',
  近7天: 'Last 7 Days',
  近15天: 'Last 15 Days',
  近30天: 'Last 30 Days',
  表格设置: 'Table Settings',
  字段显示设置: 'Field Display Settings',
  '（最多{max}项）': '(up to {max} items)',
  全选: 'Select All',
  表格行高: 'Row Height',
  小: 'Small',
  中: 'Medium',
  大: 'Large',
  默认排序表头设置: 'Default Sorting Header Settings',
  '（请在下面可排序字段中选择）': '(Please select from the sortable fields below)',
  '（': '(',
  默认: 'Default',
  升序: 'Ascending',
  降序: 'Descending',
  切换: 'Switch',
  '）': ')',
  快捷键列表: 'Shortcut Keys List',
  放大: 'Zoom In',
  缩小: 'Zoom Out',
  还原: 'Restore',
  缩放: 'Scale',
  鼠标滚动: 'Mouse Scroll',
  '连续选中（或取消）节点': 'Select (or Deselect) Nodes Continuously',
  鼠标左键单击: 'Left Click Mouse',
  移动流程元素: 'Move Process Elements',
  选中后: 'After Selection',
  '箭头（上下左右）': 'Arrow Keys (Up, Down, Left, Right)',
  删除节点: 'Delete Node',
  '复制/粘贴': 'Copy/Paste',
  '最近 x 次成功执行耗时': 'Time Taken for the Last {num} Successful Executions',
  已运行: 'Already Run',
  暂无成功执行历史: 'No Successful Execution History',
  变量引用: 'Variable Reference',
  跳过: 'Skip',
  结束: 'End',
  复制节点: 'Copy Node',
  复制并插入: 'Copy and Insert',
  解除连线: 'Disconnect',
  删除连线: 'Delete Connection',
  标准插件节点: 'Standard Plugin Node',
  子流程节点: 'Subprocess Node',
  并行网关: 'Parallel Gateway',
  分支网关: 'Branch Gateway',
  汇聚网关: 'Converging Gateway',
  条件并行网关: 'Conditional Parallel Gateway',
  开始: 'Start',
  重试: 'Retry',
  暂停: 'Pause',
  强制终止: 'Force Termination',
  继续: 'Continue',
  节点参数: 'Node Parameters',
  审批: 'Approval',
  '当前插件即将停止维护，请更新插件版本': 'The current plugin will soon stop maintenance, please update the plugin version.',
  '当前插件已停止维护，请更新插件版本': 'The current plugin has stopped maintenance, please update the plugin version.',
  '标准插件节点：': 'Standard Plugin Node:',
  '已封装好的可用插件，可直接选中拖拽至画布中。': 'Encapsulated, available plugins that can be selected and dragged directly to the canvas.',
  '子流程：': 'Subprocess:',
  '同一个项目下已新建的流程，作为子流程可以嵌套进至当前流程，并在执行任务时可以操作子流程的单个节点。': 'Newly created processes in the same project can be nested into the current process as subprocesses, and individual nodes within the subprocess can be operated during task execution.',
  '并行网关：': 'Parallel Gateway:',
  '有多个流出分支，并且多个流出分支都默认执行。': 'Multiple outgoing branches, all of which are executed by default.',
  '分支网关：': 'Branch Gateway:',
  '执行符合条件的唯一流出分支。': 'Execute the only outgoing branch that meets the conditions',
  '汇聚网关：': 'Converging Gateway:',
  '当汇聚网关用于汇聚并行网关时，所有进入顺序流的分支都到达以后，流程才会通过汇聚网关。': 'When the converging gateway is used to converge parallel gateways, the process will only pass through the converging gateway after all branches that entered the sequential flow have arrived.',
  '条件并行网关：': 'Conditional Parallel Gateway:',
  '执行时满足分支条件的都会执行。': 'All branches meeting the conditions will be executed.',
  请选择标签: 'Please Select a Tag',
  请输入插件名称: 'Please Enter Plugin Name',
  搜索结果为空: 'Search Results are Empty',
  搜索插件: 'Search Plugin',
  内置插件: 'Builtin Plugins',
  第三方插件: 'Third-Party Plugins',
  API插件: 'API Plugins',
  项目流程: 'Project Process',
  公共流程: 'Public Process',
  节点: 'Node',
  请输入流程名称: 'Please Enter Process Name',
  缩略视图: 'Thumbnail View',
  复位: 'Reset',
  节点框选: 'Node Selection',
  排版: 'Layout',
  快捷键: 'Shortcuts',
  变量引用预览: 'Variable Reference Preview',
  反选: 'Inverse Selection',
  请输入: 'Please Enter',
  条件: 'Condition',
  值: 'Value',
  查看配置: 'View Configuration',
  内容填写不完整: 'Content Incomplete',
  组合条件: 'Combined Conditions',
  序号: 'Rule Number',
  '查看字段/列': 'View Fields/Columns',
  '删除字段/列': 'Delete Fields/Columns',
  '确定删除字段/列吗？': 'Are you sure you want to delete the field/column?',
  '删除时，会将整列内容同时删除且不可撤回，请谨慎！': 'Deleting this will remove the entire column and cannot be undone, please proceed with caution.',
  删除: 'Delete',
  添加字段: 'Add Field',
  条件字段: 'Condition Field',
  结果字段: 'Result Field',
  暂未添加条件字段: 'No Condition Fields Added Yet',
  '条件字段未配置，请先完成字段配置': 'Condition fields are not configured. Please complete the field configuration first.',
  暂未添加结果字段: 'No Result Fields Added Yet',
  '结果字段未配置，请先完成字段配置': 'Result fields are not configured. Please complete the field configuration first.',
  请至少完整填写一条规则: 'Please completely fill out at least one rule',
  新增: 'Add',
  编辑: 'Edit',
  查看: 'View',
  且: 'And',
  或: 'Or',
  字段: 'Field',
  新增条件组: 'Add Condition Group',
  规则设置方式: 'Rule Setting Method',
  表单设置: 'Form Settings',
  表达式: 'Expression',
  '请填入需要的 FEEL 表达式，支持的语法可参考': 'Please enter the required FEEL expression. Supported syntax can be referenced in',
  文档: 'Documentation',
  选项名称: 'Option Name',
  选项值: 'Option Value',
  添加选项: 'Add Option',
  选项: 'Option',
  风险提示: 'Risk Warning',
  '修改或删除选项可能导致现有规则不适配，请谨慎操作': 'Modifying or deleting options may cause existing rules to become incompatible. Please proceed with caution.',
  保存: 'Save',
  请选择空间: 'Please Select a Space',
  新建空间: 'Create New Space',
  空间管理: 'Space Management',
  系统管理: 'System Management',
  中文: 'Chinese',
  产品文档: 'Documentation',
  版本日志: 'Release Notes',
  流程: 'Process',
  任务: 'Task',
  调试任务: 'Debug Task',
  决策表: 'Decision Table',
  空间配置: 'Space Configuration',
  模块配置: 'Module Configuration',
  当前版本: 'Current',
  开始节点: 'Start Node',
  结束节点: 'End Node',
  任务节点: 'Task Node',
  汇聚节点: 'Convergence Node',
  重试子流程: 'Retry Subprocess',
  跳过子流程: 'Skip Subprocess',
  导出: 'Export',
  提示: 'Tip',
  已过期: 'Expired',
  未执行: 'Not Executed',
  执行中: 'Executing',
  排队中: 'Queued',
  节点暂停: 'Node Paused',
  失败: 'Failed',
  完成: 'Completed',
  终止: 'Terminated',
  运维工具: 'Operations Tools',
  监控告警: 'Monitoring Alarm',
  配置管理: 'Configuration Management',
  开发工具: 'Development Tools',
  企业IT: 'Enterprise IT',
  办公应用: 'Office Applications',
  其它: 'Others',
  默认分类: 'Default Category',
  手动: 'Manual',
  API网关: 'API Gateway',
  轻应用: 'Light Application',
  周期任务: 'Recurring Task',
  移动端: 'Mobile',
  计划任务: 'Scheduled Task',
  '输入定时表达式非法，请校验': 'Invalid timing expression, please check',
  间隔时间必须是正整数: 'Interval must be a positive integer',
  流程名称不能为空: 'Process name cannot be empty',
  流程名称不能包含: 'Process name cannot contain',
  非法字符: 'illegal characters',
  流程名称长度不能超过: 'Process name length cannot exceed',
  个字符: 'characters',
  节点名称不能为空: 'Node name cannot be empty',
  节点名称不能包含: 'Node name cannot contain',
  节点名称长度不能超过: 'Node name length cannot exceed',
  步骤名称不能包含: 'Step name cannot contain',
  步骤名称长度不能超过: 'Step name length cannot exceed',
  变量名称不能为空: 'Variable name cannot be empty',
  变量名称不能包含: 'Variable name cannot contain',
  变量名称长度不能超过: 'Variable name length cannot exceed',
  变量KEY值不能为空: 'Variable KEY cannot be empty',
  '变量KEY由英文字母、数字、下划线组成，不允许使用系统变量及业务环境变量命名规则，且不能以数字开头': 'Variable KEY consists of letters, numbers, and underscores. System variables and business environment variable naming rules are not allowed, and it cannot begin with a number.',
  变量KEY值长度不能超过: 'Variable KEY length cannot exceed',
  变量KEY值已存在: 'Variable KEY already exists',
  变量隐藏时默认值不能为空: 'Default value cannot be empty when the variable is hidden',
  默认值不满足正则校验: 'Default value does not meet regex validation',
  正则校验不是合法的正则表达式: 'Regex validation is not a valid regex expression',
  任务名称不能为空: 'Task name cannot be empty',
  任务名称不能包含: 'Task name cannot contain',
  任务名称不能超过: 'Task name cannot exceed',
  方案名称不能为空: 'Plan name cannot be empty',
  方案名称不能包含: 'Plan name cannot contain',
  方案名称不能超过: 'Plan name cannot exceed',
  流程模板不能为空: 'Process template cannot be empty',
  应用名称不能为空: 'Application name cannot be empty',
  应用名称不能包含: 'Application name cannot contain',
  应用名称不能超过: 'Application name cannot exceed',
  应用简介不能超过: 'Application summary cannot exceed',
  定时流程名称不能为空: 'Scheduled process name cannot be empty',
  定时流程名称包含: 'Scheduled process name contains',
  定时流程名称不能超过: 'Scheduled process name cannot exceed',
  项目名称不能为空: 'Project name cannot be empty',
  项目名称包含: 'Project name contains',
  项目名称不能超过: 'Project name cannot exceed',
  项目描述不能超过: 'Project description cannot exceed',
  定时表达式不能为空: 'Cron expression cannot be empty',
  间隔时间不能为空: 'Interval time cannot be empty',
  本地缓存名称不能为空: 'Local cache name cannot be empty',
  本地缓存名称包含: 'Local cache name contains',
  本地缓存名称不能超过: 'Local cache name cannot exceed',
  '名称由英文字母、数字、下划线组成，且不能以数字开头': 'The name consists of letters, numbers, and underscores and cannot start with a number',
  名称长度不能超过: 'The name length cannot exceed',
  开始分钟数不能为空: 'Start minutes cannot be empty',
  '请输入 0 - 59 之间的数': 'Please enter a number between 0 - 59',
  开始小时数不能为空: 'Start hours cannot be empty',
  '请输入 0 - 23 之间的数': 'Please enter a number between 0 - 23',
  开始周数不能为空: 'Start week cannot be empty',
  '请输入 0 - 6 之间的数': 'Please enter a number between 0 - 6',
  开始天数不能为空: 'Start days cannot be empty',
  '请输入 1 - 31 之间的数': 'Please enter a number between 1 - 31',
  开始月数不能为空: 'Start month cannot be empty',
  '请输入 1 - 12 之间的数': 'Please enter a number between 1 - 12',
  test不能为空: 'Test cannot be empty',
  '请输入 test 之间的数': 'Please enter a number between test',
  已复制: 'Copied',
  '确认离开当前页?': 'Are you sure you want to leave this page?',
  离开将会导致未保存信息丢失: 'Leaving will cause unsaved information to be lost',
  离开: 'Leave',
  蓝鲸流程引擎平台: 'BKFlow',
  蓝鲸智云: 'Tencent BlueKing',
  展开详情: 'Expand Details',
  隐藏详情: 'Hide Details',
  复制成功: 'Copied Successfully',
  小于: 'Less Than',
  秒: 'Seconds',
  节点不可连接自身: 'Node cannot connect to itself',
  只能添加输入连线: 'Only input connections can be added',
  只能添加输出连线: 'Only output connections can be added',
  不能连接: 'Cannot Connect',
  相同节点不能回连: 'Same node cannot reconnect',
  重复添加连线: 'Duplicate connection not allowed',
  已达到: 'Has Reached',
  最大输出连线条数: 'Maximum number of output connections',
  最大输入连线条数: 'Maximum number of input connections',
  在模板中只能添加一个: 'Only one can be added in the template',
  至少需要: 'At least',
  条输入连线: 'input connections are required',
  条输出连线: 'output connections are required',
  请添加任务节点: 'Please add task nodes',
  并行网关缺少对应的汇聚网关: 'Parallel gateway lacks a corresponding convergence gateway',
  条件并行网关缺少对应的汇聚网关: 'Conditional parallel gateway lacks a corresponding convergence gateway',
  '没找到页面！': 'Page Not Found!',
  编辑空间配置: 'Edit Space Configuration',
  新增空间配置: 'Add Space Configuration',
  提交: 'Submit',
  空间名称: 'Space Name',
  '仅支持您是开发者的 app_code': 'Only supported if you are a developer of the app',
  空间描述: 'Space Description',
  平台提供服务的地址: 'Platform Service Address',
  '请提供以 https:// 或 http:// 开头的服务地址': 'Please provide a service address starting with https:// or http://',
  '修改成功！': 'Modification Successful!',
  '新增成功！': 'Addition Successful!',
  创建时间: 'Created at',
  更新时间: 'Update Time',
  开始时间: 'Start Time',
  结束时间: 'End Time',
  调试: 'Debug',
  创建任务: 'Create Task',
  字段输入: 'Field Input',
  输出结果: 'Output Results',
  关闭: 'Close',
  '确认删除该决策表？': 'Confirm deletion of this decision table?',
  '决策表名称：': 'Decision Table Name:',
  '删除后不可恢复，请谨慎操作！': 'Deletion is irreversible, please proceed with caution!',
  '当前决策表已被流程使用，禁止删除！': 'The current decision table is used by a process and cannot be deleted!',
  '决策表删除成功！': 'Decision table deleted successfully!',
  编辑决策表: 'Edit Decision Table',
  新建决策表: 'Create Decision Table',
  基础信息: 'Basic Information',
  名称: 'Name',
  关联流程: 'Related Process',
  描述: 'Description',
  规则配置: 'Rule Configuration',
  需保证有且仅有一条规则被命中: 'Ensure that one and only one rule is hit',
  '该决策表已被流程中的节点引用，仅支持修改规则，不支持修改字段(修改字段需要修改节点引用后重新保存流程)。': 'This decision table is referenced by a node in a process. Only rule modifications are supported, not field modifications (field modifications require updating the node references and saving the process again).',
  '{0}成功': '{0} Successful',
  新建: 'Add',
  '决策表{0}成功': 'Decision Table {0} Successful',
  可以回到流程里继续使用: 'You can return to the process and continue using it',
  关闭当前页: 'Close Current Page',
  '确定保存决策表并去执行调试?': 'Save decision table and execute debugging?',
  决策表详情: 'Decision Table Details',
  '名称：': 'Name:',
  '描述：': 'Description:',
  '关联流程：': 'Related Process:',
  请选择: 'Please Select',
  'ID/名称/创建人/更新人/所属模板 ID/所属作用域类型/所属作用域值': 'ID/Name/Created by/Updater/Template ID/Scope Type/Scope Value',
  操作: 'Operation',
  所属作用域类型: 'Scope Type',
  所属作用域值: 'Scope Value',
  '所属模板 ID': 'Template ID',
  创建人: 'Created by',
  更新人: 'Updater',
  恢复默认值: 'Reset',
  编辑配置: 'Edit Configuration',
  说明: 'Explanation',
  '数据格式不正确，应为JSON格式': 'Incorrect data format, should be in JSON format',
  '确认" {0} "恢复默认值?': 'Confirm to reset value for "{0}"?',
  '删除成功！': 'Deleted Successfully!',
  'ID/任务名称/创建人/执行人/所属模板 ID/所属作用域类型/所属作用域值': 'ID/Task Name/Created by/Executor/Template ID/Scope Type/Scope Value',
  引擎操作: 'Engine Operation',
  任务名称: 'Task Name',
  执行人: 'Executor',
  状态: 'Status',
  '引擎实例 Id': 'Engine Instance ID',
  已暂停: 'Paused',
  未知: 'Unknown',
  '任务删除成功！': 'Task Deleted Successfully!',
  新建任务: 'Create Task',
  模板ID: 'Template ID',
  请求参数: 'Request Parameters',
  '请求参数格式不正确，应为JSON格式': 'Request parameters are not in the correct format, should be in JSON format',
  任务创建成功: 'Task Created Successfully',
  新建流程: 'Add',
  流程名称: 'Process Name',
  流程创建成功: 'Process Created Successfully',
  'ID/流程名称/创建人/更新人/启用/所属作用域类型/所属作用域值': 'ID/Flow Name/Created by/Updater/Enabled/Scope Type/Scope Value',
  创建流程: 'Add',
  '当前已选择 x 条数据': 'Currently Selected {num} Items',
  '，': ', ',
  清除选择: 'Clear Selections',
  启用: 'Enable',
  模板名称: 'Template Name',
  本页全选: 'Select All on This Page',
  跨页全选: 'Select Across Pages',
  '流程删除成功！': 'Process Deleted Successfully!',
  '确认删除流程"{0}"?': 'Confirm deletion of process "{0}"?',
  编辑模块配置: 'Edit Module Configuration',
  新增模块配置: 'Add Module Configuration',
  '确认删除模块" {0} " ?': 'Confirm deletion of module "{0}"?',
  请求: 'Request',
  引擎版本: 'Engine Version',
  请求资源: 'Requested Resource',
  路径参数: 'Path Parameters',
  '对应接口参数及参数类型请参考【': 'For corresponding interface parameters and types, please refer to【',
  '】': '】',
  请完善请求信息: 'Please complete the request information',
  发送请求: 'Send Request',
  重置: 'Reset',
  响应: 'Response',
  请先发送请求: 'Please send a request first',
  请输入完整的路径参数: 'Please enter complete path parameters',
  'Body格式不正确，应为JSON格式': 'Body format is incorrect, should be in JSON format',
  键: 'Key',
  执行记录: 'Execution Records',
  配置快照: 'Configuration Snapshot',
  操作历史: 'Operation History',
  调用日志: 'Invocation Logs',
  第: 'Round',
  次循环: 'Cycle',
  次执行: 'Execution',
  继续执行: 'Continue Execution',
  失败后跳过: 'Skip After Failure',
  '默认值不符合正则规则：': 'Default value does not match the regex rule:',
  流程模板: 'Process Template',
  标准插件: 'Standard Plugin',
  插件版本: 'Plugin Version',
  节点名称: 'Node Name',
  步骤名称: 'Step Name',
  执行方案: 'Execution Plan',
  是否可选: 'Optional',
  是: 'Yes',
  否: 'No',
  失败处理: 'Failure Handling',
  自动跳过: 'Auto Skip',
  手动跳过: 'Manual Skip',
  手动重试: 'Manual Retry',
  在: 'In',
  后: 'After',
  自动重试: 'Auto Retry',
  次: 'Times',
  超时控制: 'Timeout Control',
  总是使用最新版本: 'Always Use Latest Version',
  输入参数: 'Input Parameters',
  暂无参数: 'No Parameters',
  输出参数: 'Output Parameters',
  强制终止后跳过: 'Skip After Force Termination',
  超时: 'Timeout',
  则: 'Then',
  节点执行记录: 'Node Execution Records',
  异常信息: 'Exception Information',
  任务名: 'Task Name',
  耗时: 'Elapsed Time',
  详情: 'Details',
  显示全部: 'Show All',
  暂无异常: 'No Exceptions',
  执行信息: 'Execution Information',
  子流程详情: 'Subprocess Details',
  暂无执行信息: 'No Execution Information',
  参数名: 'Parameter Name',
  参数值: 'Parameter Value',
  平台日志: 'Platform Logs',
  第三方插件日志: 'Third-Party Plugin Logs',
  暂无日志: 'No Logs',
  操作时间: 'Operation Time',
  操作类型: 'Operation Type',
  操作来源: 'Operation Source',
  操作人: 'Operator',
  暂无输出: 'No Output',
  参数Key: 'Parameter Key',
  请选择执行分支: 'Please Select Execution Branch',
  可选执行分支: 'Optional Execution Branches',
  注入全局变量: 'Inject Global Variables',
  '请输入变量的KEY,如${KEY}': 'Please enter the variable KEY, e.g., ${KEY}',
  请输入变量的值: 'Please enter the variable value',
  请选择变量值的类型: 'Please select the type of the variable value',
  字符串: 'String',
  整数: 'Integer',
  字典: 'Dictionary',
  变量Value值不能为空: 'Variable value cannot be empty',
  变量Value格式不正确: 'Variable value format is incorrect',
  至少保留一条全局变量: 'At least one global variable must be retained',
  保存并继续: 'Save and Continue',
  去修改: 'Go to Modify',
  暂停去修改: 'Pause to Modify',
  '参数已被使用，不可修改': 'Parameter is already in use and cannot be modified',
  任务已暂停执行: 'Task has been paused',
  修改入参: 'Modify Input Parameters',
  参数未修改: 'Parameter not modified',
  '参数未修改，任务已继续执行': 'Parameter not modified, task has been continued',
  '保存失败，有参数已被使用不可修改': 'Save failed, some parameters are in use and cannot be modified',
  参数修改成功: 'Parameter modified successfully',
  '参数修改成功，任务已继续执行': 'Parameter modified successfully, task has been continued',
  修改成功: 'Modified Successfully',
  '退回节点：': 'Revert Node:',
  来源: 'Source',
  参数明细: 'Parameter Details',
  分支类型: 'Branch Type',
  自定义分支: 'Custom Branch',
  默认分支: 'Default Branch',
  feelOperates: 'Supports binary comparison operators such as =, !=, >, >=, <, <=',
  '支持 "and、or、true、false" 等关键字语法': 'Supports keywords such as and, or, true, false',
  '支持 FEEL (Friendly Enough Expression Language) 基础语法': 'Supports basic FEEL (Friendly Enough Expression Language) syntax',
  '支持使用全局变量，如': 'Supports using global variables, such as',
  '${}中支持使用内置函数、datetime、re、hashlib、random、time、os.path模块处理全局变量': 'support using built-in functions, datetime, re, hashlib, random, time, os in ${}',
  '因 MAKO 语法限制：请勿使用 \'$\', \'{\' 和 \'}\' 字符': 'Due to MAKO syntax limitations: please do not use the characters \'$\', \'{\', and \'}\'',
  '示例：': 'Example:',
  '字符串比较：': 'String Comparison:',
  '数值比较：': 'Numerical Comparison:',
  '包含：': 'Contains:',
  makoOperates: 'Supports binary comparison operators such as ==, !=, >, >=, <, <=, in, notin',
  '支持 "and、or、True/true、False/false" 等关键字语法': 'Supports keywords such as and, or, True/true, False/false',
  表达式更多细节请参考: 'For more details on expressions, please refer to',
  '支持使用内置函数、datetime、re、hashlib、random、time、os.path模块处理全局变量': 'support using built-in functions, datetime, re, hashlib, random, time, os',
  '假设 key 是值为 "3" 的全局变量': 'Assume key is a global variable with a value of \'3\'',
  '使用内置函数、datetime、re、hashlib、random、time、os.path模块处理全局变量': 'Support using built-in functions, datetime, re, hashlib, random, time, os',
  '支持 "and、or、not、True、False" 等 MAKO 关键字语法': 'Supports MAKO keywords such as and, or, not, True, False',
  支持直接引用全局变量: 'Support Direct Reference to Global Variables',
  '所有分支均不匹配时执行，类似switch-case-default里面的default': 'Executed when no branches match, similar to default in switch-case-default',
  任务执行信息: 'Task Execution Information',
  '存在子流程节点执行失败，可从节点执行记录去往子任务处理，并及时': 'Subprocess node execution failed, can go to the subtask from the node execution records and promptly',
  刷新任务状态: 'Refresh Task Status',
  '。': '.',
  审批意见: 'Approval Comments',
  通过: 'Approve',
  拒绝: 'Reject',
  备注: 'Remarks',
  执行: 'Execute',
  任务开始执行: 'Task Started Execution',
  任务已继续执行: 'Task has continued execution',
  任务终止成功: 'Task Termination Successful',
  跳过成功: 'Skip Successful',
  强制终止执行成功: 'Force Termination Execution Successful',
  继续成功: 'Continue Successful',
  '确定重试当前节点？': 'Confirm retry of the current node?',
  '重新节点将重新执行当前节点（使用最新的参数值）': 'Retrying node will re-execute the current node (using the latest parameter values)',
  重试成功: 'Retry Successful',
  '确定跳过当前节点?': 'Confirm skip of the current node?',
  跳过节点将忽略当前失败节点继续往后执行: 'Skipping the node will ignore the current failed node and continue execution',
  '确定强制终止当前节点?': 'Confirm force termination of the current node?',
  '强制终止将强行修改节点状态为失败，但不会中断已经发送到其它系统的请求': 'Forcing termination will change the node status to failed, but will not interrupt requests already sent to other systems',
  修改时间: 'Modification Time',
  '确定继续往后执行?': 'Confirm to Continue Execution?',
  并行: 'Parallel',
  '确定终止当前任务?': 'Confirm terminate the current task?',
  '终止任务将停止执行任务，但执行中节点将运行完成': 'Terminating the task will stop its execution, but nodes already in execution will run to completion',
  节点详情: 'Node Details',
  注入全局变量成功: 'Global Variables Injected Successfully',
  任务执行: 'Task Execution',
  查看流程: 'View Process',
  任务入参: 'Task Input Parameters',
  查看节点详情: 'View Node Details',
  操作记录: 'Operation Records',
  跳转任务列表: 'Jump To Task List',
  全局变量: 'Global Variables',
  '设为「常量」的参数中途不允许修改': 'Parameters set as "constants" cannot be modified midway',
  '参数值不符合正则规则：': 'Parameter value does not match regex rule:',
  批量更新子流程: 'Batch Update Subprocesses',
  '子流程更新时，如果新旧版本存在相同表单，表单数据会默认取原表单数据': 'When updating subprocess, if the same forms exist in both old and new versions, the form data will default to the original data',
  原版本: 'Original Version',
  待更新版本: 'Version to be Updated',
  已选择: 'Selected',
  个: 'items',
  待更新的子流程: 'Subprocesses to be Updated',
  批量更新: 'Batch Update',
  '全局变量【 x 】的引用数已为 0。如果不再使用，可立即删除变量; 也可以稍后在全局变量面板中删除': 'The reference count of global variable 【 {key} 】 is 0. If no longer used, it can be deleted immediately or later in the global variables panel',
  删除变量: 'Delete Variable',
  以后再说: 'Remind Me Later',
  头部区域: 'Header Area',
  分支条件: 'Branch Conditions',
  分支名称: 'Branch Name',
  '表达式语法与当前空间下配置的解析语法 [ {0} ]不匹配，保存失败。请重新添加网关，已存在的网关仍按原有语法解析。': 'The expression syntax does not match the parsing syntax configured under the current space [ {0} ], saving failed. Please re-add the gateway, existing gateways will still be parsed using the original syntax.',
  '确定保存修改的内容？': 'Are you sure you want to save the changes?',
  当前流程模板在浏览器多个标签页打开: 'The current process template is open in multiple browser tabs.',
  双击左键: 'Double-click the left mouse button',
  可以快捷打开节点配置面板: 'You can quickly open the node configuration panel',
  '确定保存流程并去设置执行方案？': 'Are you sure you want to save the process and set the execution plan?',
  '确定保存克隆流程并去设置执行方案？': 'Are you sure you want to save the cloned process and set the execution plan?',
  '自定义变量中存在系统变量/项目变量的key，需要清除后才能保存，是否一键清除？(可通过【模版数据-constants】进行确认)': 'There are system/project variable keys in the custom variables. They need to be cleared before saving. Would you like to clear them all at once? (This can be confirmed through [Template Data - Constants])',
  '问题变量有：': 'The problem variables are:',
  保存成功: 'Save Successful',
  '任务节点参数错误，请点击错误节点查看详情': 'Task node parameter error, please click on the error node to view details',
  请选择节点的插件类型: 'Please select the plugin type of the node',
  请选择节点的子流程: 'Please select the subprocess of the node',
  '表单值中不存在 {0} 属性': 'The attribute {0} does not exist in the form value',
  '排版完成，原内容在本地快照中': 'Layout complete, original content is in the local snapshot',
  '确定保存流程并去调试?': 'Are you sure you want to save the process and go to debug?',
  方案保存成功: 'Plan Saved Successfully',
  新增流程本地快照成功: 'New Process Local Snapshot Created Successfully',
  '最近快照已保存，并恢复至原序号为': 'The latest snapshot has been saved and restored to the original serial number',
  的快照: 'the snapshot',
  替换流程成功: 'Replace Process Successful',
  自动保存: 'Auto Save',
  '系统不会保存您所做的更改，确认离开？': 'The system will not save your changes, are you sure you want to leave?',
  重选: 'Reselect',
  选择: 'Select',
  节点标签: 'Node Labels',
  '未选择失败处理方式，标准插件节点如果执行失败，会导致任务中断后不可继续': 'Failure handling method not selected. If the standard plugin node fails, it will interrupt the task and cannot continue.',
  '自动跳过：标准插件节点如果执行失败，会自动忽略错误并把节点状态设置为成功。': 'Auto Skip: If the standard plugin node fails, it will automatically ignore the error and set the node status to successful.',
  '手动跳过：标准插件节点如果执行失败，可以人工干预，直接跳过节点的执行。': 'Manual Skip: If the standard plugin node fails, it can be manually intervened to directly skip the node execution.',
  '手动重试：标准插件节点如果执行失败，可以人工干预，填写参数后重试节点。': 'Manual Retry: If the standard plugin node fails, it can be manually intervened to retry the node after filling in parameters.',
  '自动重试：标准插件节点如果执行失败，系统会自动以原参数进行重试。': 'Auto Retry: If the standard plugin node fails, the system will automatically retry with the original parameters.',
  执行代理人: 'Execution Agent',
  请输入用户: 'Please enter the user',
  '子流程有更新，更新时若存在相同表单数据则获取原表单的值。': 'The subprocess has been updated. If the same form data exists during the update, the original form value will be taken.',
  更新子流程: 'Update Subprocess',
  每次创建任务会使用选中执行方案的最新版本且不会提示该节点需要更新: 'Each time a task is created, the latest version of the selected execution plan will be used and the node will not prompt for updates.',
  '此流程无执行方案，无需选择': 'This process has no execution plan and no selection is required.',
  该功能仅对V2引擎生效: 'This feature is only effective for the V2 engine.',
  '打开此功能后，每次创建任务会尝试使用子流程的最新版本，并且不会再提示该节点需要更新。': 'Enabling this feature will try to use the latest version of the subprocess each time a task is created, and there will be no prompt for node updates.',
  '若子流程中发生变动，标准运维会采用以下处理策略，如处理不符合预期，请谨慎使用。': 'If changes occur in the subprocess, the standard operation will adopt the following handling strategies. If the handling is not as expected, please use it with caution.',
  请选择插件: 'Please select a plugin',
  请选择插件版本: 'Please select a plugin version',
  请选择流程模板: 'Please select a process template',
  不使用执行方案: 'Do not use an execution plan',
  '无法获取决策表配置数据，引用的决策表可能已被删除': 'Unable to obtain decision table configuration data. The referenced decision table may have been deleted.',
  '切换决策表输出参数将被清除，是否继续切换？': 'Switching the decision table will clear the output parameters. Do you want to continue switching?',
  '无匹配数据，可【回车】插入后新建变量': 'No matching data, you can press [Enter] to insert and create a new variable',
  取消变量快捷输入: 'Cancel Variable Quick Input',
  变量快捷输入: 'Variable Quick Input',
  查看未引用变量: 'View Unreferenced Variables',
  未找到可用的插件或插件版本: 'No available plugins or plugin versions found',
  重选插件: 'Reselect Plugin',
  请选择子流程: 'Please select a subprocess',
  json数据格式不正确: 'JSON data format is incorrect',
  勾选为全局变量: 'Check as Global Variable',
  取消接收输出: 'Cancel Receiving Output',
  使用变量接收输出: 'Use Variable to Receive Output',
  变量配置: 'Variable Configuration',
  '已存在相同KEY的变量，请新建变量': 'A variable with the same KEY already exists, please create a new variable',
  创建方式: 'Creation Method',
  自动创建: 'Auto Create',
  变量复用: 'Variable Reuse',
  手动创建: 'Manual Create',
  复用变量: 'Reuse Variable',
  变量名称: 'Variable Name',
  变量KEY: 'Variable KEY',
  '变量KEY为特殊标志符变量，请修改': 'Variable KEY is a special identifier, please modify it',
  '找不到想要的插件？可以尝试自己动手开发！': 'Can\'t find the plugin you want? Try developing it yourself!',
  由: 'By',
  提供: 'Provided',
  请选择流程进行节点配置: 'Please select the process for node configuration',
  标签: 'Tags',
  节点配置: 'Node Configuration',
  新建变量: 'Create Variable',
  属性: 'Attributes',
  输入: 'Input',
  输出: 'Output',
  显示: 'Show',
  隐藏: 'Hide',
  选择操作: 'Select Operation',
  请填写完整参数: 'Please fill in complete parameters',
  生成并复制代码: 'Generate and Copy Code',
  本地快照: 'Local Snapshot',
  '可自动保存最近的50次快照，每5分钟一次。仅在本地浏览器存储。': 'Automatically save the latest 50 snapshots every 5 minutes. Only stored in the local browser.',
  查看需要更新的子流程: 'View Subprocesses That Need Updates',
  '建议及时处理子流程更新，涉及': 'It is recommended to promptly handle subprocess updates, involving',
  个子流程节点: 'subprocess nodes',
  编辑执行方案: 'Edit Execution Plan',
  个节点: 'nodes',
  导出图片: 'Export Image',
  关闭预览: 'Close Preview',
  创建任务: 'Create Task',
  编辑流程: 'Edit Process',
  保存并新建任务: 'Save and Create New Task',
  项目: 'Project',
  不允许添加没有节点的执行方案: 'Cannot add an execution plan without nodes',
  成功: 'Success',
  通知人: 'Notify to',
  请选择通知人: 'Please select a notifier',
  通知方式: 'Notification Method',
  通知分组: 'Notification Grouping',
  管理项目变量: 'Manage Project Variables',
  变量快捷处理: 'Quick Variable Handling',
  '属性：': 'Attributes:',
  '"来源/是否显示"格式，来源是输入类型': '"Source/Show" format, source is input type',
  '表示变量来自用户添加的变量或者标准插件/子流程节点输入参数引用的变量，来源是输出类型': 'Indicates that the variable is from a user-added variable or an input parameter referenced by a standard plugin/subprocess node, source is output type',
  '表示变量来自标准插件/子流程节点输出参数引用的变量；是否显示表示该变量在新建任务填写参数时是否展示给用户，': 'Indicates that the variable is from a standard plugin/subprocess node output parameter reference; whether the variable is displayed means whether it is shown to the user when filling in parameters for a new task,',
  '表示显示，': 'indicates show,',
  '表示隐藏，输出类型的变量一定是隐藏的。': 'indicates hide, output type variables are always hidden.',
  '输出：': 'Output:',
  '表示该变量会作为该流程模板的输出参数，在被其他流程模板当做子流程节点时可以引用。': 'Indicates that this variable will be used as an output parameter for the process template and can be referenced when used as a subprocess node in other process templates.',
  '模板预渲染：': 'Template Pre-rendering:',
  '模板预渲染为“是”时，任务会在执行前将变量中的 MAKO 段进行渲染，': 'If template pre-rendering is "yes", the MAKO segments in the variables will be rendered before the task execution,',
  '而不是在第一个引用该变量的节点执行前才进行渲染；': 'rather than being rendered before the node that first references the variable executes;',
  '如果需要预渲染的变量引用了别的变量，': 'if the variable that needs pre-rendering references other variables,',
  '那么被引用变量的预渲染也要设置为“是”，否则预渲染不生效。': 'then the pre-rendering of the referenced variables must also be set to "yes", otherwise the pre-rendering will not be effective.',
  跨流程克隆: 'Cross-process Cloning',
  已选择x项: '{num} items selected',
  隐藏系统变量: 'Hide System Variables',
  引用: 'Reference',
  '直接引用全局变量的节点数量，点击数字查看引用详情': 'Number of nodes directly referencing global variables; click the number to view reference details',
  '显示（入参）': 'Show (Input)',
  '无数据，请手动新增变量或者勾选标准插件参数自动生成': 'No data available, please manually add variables or check the standard plugin parameters for automatic generation',
  系统变量: 'System Variables',
  项目变量: 'Project Variables',
  节点输出: 'Node Output',
  节点输入: 'Node Input',
  全部: 'All',
  变量: 'Variables',
  确认删除: 'Confirm Deletion',
  '确认删除所选的x个变量?': 'Confirm deletion of the selected {num} variables?',
  '若该变量被节点引用，请及时检查并更新节点配置': 'If the variable is referenced by nodes, please check and update the node configuration promptly',
  引用变量的: 'Referenced by the following variables:',
  请输入关键字: 'Please enter keywords',
  申请权限: 'Request Permission',
  请选择流程模版: 'Please select a process template',
  '：': ':',
  变量信息: 'Variable Information',
  '无法克隆此变量，因克隆后变量长度超限': 'Cannot clone this variable because the length exceeds the limit after cloning',
  该流程暂无自定义全局变量: 'This process has no custom global variables',
  全选所有变量: 'Select All Variables',
  '已选择 x 个变量': '{num} variables have been selected',
  下一步: 'Next',
  上一步: 'Previous',
  选择流程: 'Select Process',
  选择变量: 'Select Variables',
  全部分类: 'All Categories',
  '变量克隆成功！': 'Variable Cloning Successful!',
  正则校验: 'Regex Validation',
  '配置为「显示」可在执行时做为任务入参使用，配置为「隐藏」则仅能在流程内部使用': 'Configured as "show" can be used as task input parameters during execution, configured as "hide" can only be used internally in the process',
  执行时显示: 'Show during execution',
  '当满足条件时，原本做为入参的变量会隐藏起来无需录入': 'When conditions are met, the variable originally used as input will be hidden and no input is needed',
  '“显示参数”条件隐藏': '"Show Parameter" Conditional Hide',
  开启: 'Enable',
  '所有变量值都会以字符串类型进行记录和判断，会忽略类型差异': 'All variable values will be recorded and judged as string types, ignoring type differences',
  触发条件: 'Trigger Condition',
  '注意：如果命中条件，变量会保留填参页面的输入值并隐藏。如果变量为表单必填参数且输入值为空，可能会导致任务执行失败': 'Note: If the condition is met, the variable will retain the input value from the parameter filling page and be hidden. If the variable is a required form parameter and the input value is empty, it may cause task execution to fail',
  '常量在任务启动就完成变量值的计算，使用变量时不再重新计算保持值不变': 'Constants complete the calculation of variable values upon task initiation, and their values remain unchanged during use',
  常量: 'Constant',
  配置: 'Configuration',
  默认值: 'Default Value',
  返回: 'Return',
  '隐藏（非入参）': 'Hide (Not Input)',
  即将下线: 'About to be Decommissioned',
  已下线: 'Decommissioned',
  组件: 'Component',
  变量未找到: 'Variable Not Found',
  关系组内的数据不能为空: 'Data within the relationship group cannot be empty',
  普通变量: 'General Variable',
  动态变量: 'Dynamic Variable',
  元变量: 'Meta Variable',
  默认值不符合正则规则: 'Default value does not match regex rule',
  至少保留一条触发条件: 'At least one trigger condition must be retained',
  点击可快速定位原节点: 'Click to quickly locate the original node',
  克隆: 'Clone',
  操作名称: 'Operation Name',
  基础: 'Basis',
  请输入流程模板名称: 'Please enter the process template name',
  新建标签: 'New Label',
  标签管理: 'Label Management',
  '模板分类即将下线，建议使用标签': 'Template classification is about to be decommissioned, it is recommended to use labels',
  通知: 'Notification',
  '选择通知方式后，将默认通知到任务执行人': 'After selecting the notification method, it will default to notify the task executor',
  任务状态: 'Task Status',
  其他: 'Other',
  推荐留空使用: 'Recommend leaving it blank for use',
  项目执行代理人设置: 'Project Execution Agent Settings',
  '以便统一管理，也可单独配置流程执行代理人覆盖项目的设置': 'For unified management, you can also configure execution agents for the process individually to override project settings',
  请输入流程模板备注信息: 'Please enter process template comments',
  任务类型偏好: 'Task Type Preference',
  默认任务: 'Default Task',
  职能化任务: 'Functional Task',
  标签名称: 'Label Name',
  标签颜色: 'Label Color',
  选择颜色: 'Select Color',
  标签描述: 'Label Description',
  标签名称不能超过: 'Label name cannot exceed',
  '标签已存在，请重新输入': 'Label already exists, please re-enter',
  标签新建成功: 'New Label Created Successfully',
  '项目执行代理人(n)；免代理用户(m)': 'Project Execution Agent ({n}); No Proxy User ({m})',
  保存时间: 'Save Time',
  '无数据，请手动添加快照或等待自动保存': 'No data, please manually add snapshots or wait for automatic saving',
  流程调试: 'Process Debugging',
  调试执行: 'Debug Execution',
  退出调试: 'Exit Debugging',
  执行调试: 'Execute Debugging',
  '自动载入上次调试选择的节点？': 'Automatically load the nodes selected in the last debug?',
  载入: 'Load',
  请至少选择一个节点: 'Please select at least one node',
  '确定保存Mock数据并去执行调试?': 'Confirm to save Mock data and execute debugging?',
  模板mock数据保存成功: 'Template Mock data saved successfully',
  填写调试入参: 'Fill in the debug input parameters',
  '选择 Mock 数据': 'Select Mock Data',
  '无需Mock，真执行': 'No need for Mock, execute truly',
  '收起未设置 Mock数据 的节点': 'Collapse nodes without Mock data set',
  '显示未设置 Mock数据 的节点': 'Show nodes without Mock data set',
  '后，则': 'after, then',
  '，间隔': ', interval',
  '你可以设置多份 Mock 数据方案，用于不同调试场景。': 'You can set up multiple Mock data schemes for different debugging scenarios.',
  新增数据方案: 'Add Data Scheme',
  设为默认: 'Set as Default',
  暂无字段可设置: 'No Fields to Set',
  收起未勾选的字段: 'Collapse Unchecked Fields',
  显示未勾选的字段: 'Show Unchecked Fields',
  '暂无输出字段，Mock方案输出为空': 'No output fields available, Mock scheme output is empty',
  暂无方案: 'No Schemes Available',
  立即新增: 'Add Now',
  'Mock 数据方案': 'Mock Data Scheme',
  '设置 Mock 数据': 'Set Mock Data',
  Mock数据: 'Mock Data',
  插件配置: 'Plugin Configuration',
  流程控制选项: 'Process Control Options',
  '确认删除任务"{0}"?': 'Confirm deletion of task "{0}"?',
  '确认删除所选的 {0} 项流程吗?': 'Confirm deletion of the selected {0} processes?',
  等于: 'Equals',
  不等于: 'Not equals',
  包含: 'Contains',
  不包含: 'not contains',
  大于: 'Greater than',
  小于: 'Less than',
  大于等于: 'Greater than or equal',
  小于等于: 'Less than or equal',
  在范围内: 'In range',
  不在范围内: 'Not in range',
  编辑字段: 'Edit Field',
  复制字段: 'Copy Field',
  向左插入1列: 'Insert 1 Column Left',
  向右插入1列: 'Insert 1 Column Right',
  复制行: 'Copy Row',
  组合条件设置: 'Set Combined Conditions',
  向上插入1列: 'Insert 1 Row Above',
  向下插入1列: 'Insert 1 Row Below',
  删除行: 'Delete Row',
  字段类型: 'Field Type',
  文本: 'Text',
  数值: 'Int',
  单选下拉: 'Select',
  字段名称: 'Field Name',
  字段标识: 'Field Identifier',
  提示文字: 'Placeholder',
  描述信息: 'Description',
  选项: 'Options',
  projectPresentationTips: 'An {0} and {1} process engine platform that helps the system quickly obtain {2} ability.',
  高效: 'efficient',
  灵活: 'flexible ',
  流程执行: 'process execution',
  立即体验: 'Try it now',
  核心功能: 'Core Features',
  流程编排和画布嵌入: 'Process Orchestration',
  '直观地创建、编辑和管理流程，支持自定义权限控制。': 'Intuitively create, edit, and manage processes with custom permission controls.',
  流程任务执行能力: 'Process Execution',
  '通过 API 实现流程任务的创建、执行和控制。': 'Process can be created, executed, and controlled through APIs.',
  决策引擎能力: 'Decision Engine',
  '可在流程中进行规则管理和决策。': 'Rules management and decision-making can be done in the process.',
  '便捷接入 & 集成': 'Easy Access & Integration',
  '多种接入方式：': 'Multiple access methods:',
  '支持不同的接入方式，满足不同系统的接入需求。': 'meet the requirements of different systems.',
  '多种集成方式：': 'Multiple integration methods:',
  '支持 Web 服务集成和 SDK 集成，方便接入平台灵活集成。': 'support both Service and SDK integration.',
  '流程引擎：': 'Process Engine:',
  '决策引擎：': 'Decision Engine:',
  'FEEL 表达式解析器：': 'FEEL Expression Parser:',
  '接入平台扩展 & 管理': 'Access Platform Extension & Management',
  '高度可扩展&自定义能力': 'Highly extensible & customizable',
  '支持蓝鲸插件和 API 插件，': 'Support bk-plugin and API plugin, ',
  '满足各种接入平台业务场景上的自定义需求。': 'meet the custom requirements of various access platform business scenarios.',
  数据管理能力: 'Data Management Ability',
  '提供空间数据管理端，': 'Provide space data management, ',
  '接入平台管理员可基于空间视图进行资源数据管理和操作。': 'the access platform administrator can manage and operate resource data based on the space view.',
  '支持 Webhook 订阅机制': 'Webhook Subscription',
  '接入平台可以轻松感知，': 'The access platform can be easily perceived, ',
  '流程任务事件并进行自动化扩展。': 'process and task events and automate scaling.',
  支持计算和存储资源隔离: 'Computing & Storage Resource Isolation',
  '任务执行和数据可与其他接入空间隔离。': 'Task execution data can be isolated from other access spaces.',
  退出登录: 'Sign Out',
  首页: 'Home',
  字段标识已经存在: 'Field identifier already exists',
  key值只能由数字和字母组成且不能以数字开头: 'Key value can only consist of letters and numbers and cannot start with a number',
  必填项不能为空: 'Required field cannot be empty',
  请求成功: 'Request success',
  '请求异常（外部系统错误或非法操作）': 'Request exception (external system error or illegal operation)',
  '请求异常（内部系统发生未知错误）': 'Request exception (unknown error occurred in the internal system)',
  秒: 'Sec | one second | {n} seconds',
  分钟: 'Minute | one minute | {n} minutes',
  小时: 'Hour | one hour | {n} hours',
  天: 'Day | one day | {n} days',
  新建全局变量: 'Add Global Variables',
  error_handle_秒: 'Sec Interval',
  流程的入参: 'Input Parameters of the Process',
  scopeType: 'Indicates the type of the scope to which the corresponding resource belongs in the access platform. For example, if the resource belongs to Business 1, the value of this field can be set to "project".',
  scopeValue: 'Indicates the value of the scope to which the corresponding resource belongs in the access platform. For example, if the resource belongs to Business 1, the value of this field can be set to "1".',
  名字: 'Name',
  mockReuseTips: 'Some variables and Mock schemes have changed. Continuing will {0} the following mismatched data:',
  忽略: 'ignore',
  继续复用: 'Continue Reuse',
  '调试数据已发生变化，请确认是否继续复用': 'Debug data has changed, please confirm whether to continue reusing',
  当前调试任务: 'Current Debug Task',
  待复用调试任务: 'Pending Reuse Debug Task',
  复用成功: 'Reuse success',
  id或name不唯一: 'ID or name is not unique',
  '暂不支持带有英文双引号(") 的输入值': 'Input values containing English double quotes (\") are not supported at the moment',
  变量列表: 'Variable List',
  被赋值变量: 'Assigned Variable',
  新建凭证: 'Credential Create',
  编辑凭证: 'Credential Edit',
  凭证管理: 'Credential Management',
  内容: 'Content',
  '凭证删除后不可恢复，确认删除？': 'The voucher cannot be recovered once deleted. Are you sure you want to delete it?',
  值类型: 'Value Type',
  表单模式: 'Form Mode',
  json模式: 'JSON Mode',
  插件名称: 'Plugin Name',
  管理员: 'Administrator',
  授权状态: 'Authorization Status',
  使用范围: 'Scope of Use',
  授权状态修改时间: 'Authorization Status Modification Time',
  授权状态修改人: 'Authorization Status Modified By',
  我的插件: 'My Plugins',
  '搜索插件名称、code、授权状态、管理员、授权状态修改人': 'Search Plugin Name, Code, Authorization Status, Administrator, Authorizer',
  编辑使用范围: 'Edit Scope of Use',
  未授权: 'Unauthorized',
  蓝鲸插件: 'Blue Whale Plugin',
  授权: 'Authorize',
  '确认授权？': 'Confirm Authorization?',
  授权成功: 'Authorization Successful',
  '授权后，授权空间可以直接调用插件接口并执行插件功能。': 'After authorization, the authorized space can directly call plugin interfaces and execute plugin functions.',
  '注意：空间下的执行人信息由 BKFlow 的对接系统管理并传入，BKFlow 仅进行信息传递，不保证执行人信息的真实性。请尽量保证插件仅适用于平台对接场景，勿把传递的执行人信息作为鉴权依据。': 'Note: The executor information in the space is managed and passed by the integration system of BKFlow. BKFlow only transmits this information and does not guarantee its authenticity. Please ensure the plugin is solely used for platform integration scenarios and do not use transmitted executor information as an authorization basis.',
  '插件名称：': 'Plugin Name:',
  已授权: 'Authorized',
  取消授权: 'Revoke Authorization',
  '确认取消授权？': 'Confirm Revoke Authorization?',
  '取消后，流程配置页面将不能再看到对应的插件信息。': 'After revocation, the corresponding plugin information will no longer be visible on the process configuration page.',
  '注意：对于已经配置在流程中的插件节点，BKFlow 仍然会触发调用。如果希望完全不提供给 BKFlow 调用，请直接在 PaaS 开发者中心取消对 BKFlow 的应用授权。': 'Note: BKFlow will still trigger calls for plugin nodes already configured in the process. To completely prevent BKFlow from calling, please directly revoke the application authorization to BKFlow in the PaaS Developer Center.',
  取消授权成功: 'Authorization Revoked Successfully',
  '是否复制该流程？': 'Do you want to copy this process?',
  '注意：关联的 mock 数据不会同步复制，暂不支持复制带有决策表节点的流程': 'Note: Associated mock data will not be copied, and copying processes with decision table nodes is not supported.',
  '流程复制成功！': 'Process copied successfully!',
  使用范围编辑成功: 'Scope of Use Edited Successfully',
  '* (对所有空间公开)': '* (All Spaces)',
};

export default en;
