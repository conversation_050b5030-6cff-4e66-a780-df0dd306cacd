@mixin advancedSearch {
  .task-fieldset {
      width: 100%;
      margin-bottom: 15px;
      padding: 8px;
      border: 1px solid $commonBorderColor;
      background: #fff;
      .task-query-content {
        display: flex;
        flex-wrap: wrap;
        .query-content {
          min-width: 420px;
          padding: 10px;
          @media screen and (max-width: 1420px){
            min-width: 380px;
          }
          .query-span {
            float: left;
            min-width: 130px;
            margin-right: 12px;
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            text-align: right;
            @media screen and (max-width: 1420px){
              min-width: 100px;
            }
          }
          input {
            max-width: 260px;
            height: 32px;
            line-height: 32px;
          }
          .bk-selector-icon.clear-icon {
            top:7px;
          }
          input::-webkit-input-placeholder{
            color: $formBorderColor;
          }
          input:-moz-placeholder {
            color: $formBorderColor;
          }
          input::-moz-placeholder {
            color: $formBorderColor;
          }
          input:-ms-input-placeholder {
            color: $formBorderColor;
          }
          input{
            min-width: 260px;
          }
          .bk-selector-search-item > input {
            min-width: 249px;
          }
          .search-input {
            width: 260px;
            height: 32px;
            padding: 0 10px 0 10px;
            font-size: 14px;
            border: 1px solid $commonBorderColor;
            line-height: 32px;
            outline: none;
            &:hover {
              border-color: #c0c4cc;
            }
            &:focus {
              border-color: $blueDefault;
              & + i {
                color: $blueDefault;
              }
            }
          }
          .search-input.placeholder {
            color: $formBorderColor;
          }
        }
      }
      .query-button {
        padding: 10px;
        min-width: 450px;
        @media screen and (max-width: 1420px) {
          min-width: 390px;
        }
        text-align: center;
        .query-cancel {
          margin-left: 5px;
        }
        .bk-button {
          height: 32px;
          line-height: 32px;
        }
      }
  }
}