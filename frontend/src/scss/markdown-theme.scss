.markdown-theme-style {
  font-size: 14px;
  color: #313238;

  h1,
  h2,
  h3,
  h4,
  h5 {
    height: auto;
    margin: 10px 0;
    font: normal 14px/1.5 "Helvetica Neue", Helvetica, Arial,
        "Lantinghei SC", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
    font-weight: bold;
    color: #34383e;
  }

  h1 {
    font-size: 30px;
  }

  h2 {
    font-size: 24px;
  }

  h3 {
    font-size: 18px;
  }

  h4 {
    font-size: 16px;
  }

  h5 {
    font-size: 14px;
  }

  em {
    font-style: italic;
  }

  div,
  p,
  font,
  span,
  li {
    line-height: 1.3;
  }

  p {
    margin: 0 0 1em;
  }

  table,
  table p {
    margin: 0;
  }

  ul,
  ol {
    padding-left: 40px;
    margin: 10px 0 10px;
    text-indent: 0;
  }

  ul {
    list-style: disc;
    & > li {
      line-height: 1.8;
      white-space: normal;
      list-style: disc;
    }
  }

  ol {
    list-style: decimal;
    & > li {
      line-height: 1.8;
      white-space: normal;
      list-style: decimal;
    }
  }
  li > ul {
    margin-bottom: 10px;
  }

  li ol {
    padding-left: 20px !important;
  }

  ul ul,
  ul ol,
  ol ol,
  ol ul {
    margin-bottom: 0;
    margin-left: 20px;
  }

  pre,
  code {
    width: 95%;
    padding: 0 3px 2px;
    font-family: Monaco, Menlo, Consolas, "Courier New", monospace;
    font-size: 14px;
    color: #333;
    border-radius: 3px;
  }

  code {
    padding: 2px 4px;
    font-family: Consolas, monospace, tahoma, Arial;
    color: #d14;
    border: 1px solid #e1e1e8;
  }

  pre {
    display: block;
    padding: 9.5px;
    margin: 0 0 10px;
    font-family: Consolas, monospace, tahoma, Arial;
    font-size: 13px;
    word-break: break-all;
    word-wrap: break-word;
    white-space: pre-wrap;
    background-color: #f6f6f6;
    border: 1px solid #ddd;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    code {
      padding: 0;
      white-space: pre-wrap;
      border: 0;
    }
  }

  blockquote {
    padding: 0 0 0 14px;
    margin: 0 0 20px;
    border-left: 5px solid #dfdfdf;
    ::before,
    ::after {
      content: "";
    }
    p {
      margin-bottom: 0;
      font-size: 14px;
      font-weight: 300;
      line-height: 25px;
    }
    small {
      display: block;
      line-height: 20px;
      color: #999;
      ::before {
        content: "\2014 \00A0";
      }
    }
  }
}
