/**
* <PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community
* Edition) available.
* Copyright (C) 2017 THL A29 Limited, a Tencent company. All rights reserved.
* Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
* http://opensource.org/licenses/MIT
* Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
* an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
* specific language governing permissions and limitations under the License.
*/
import Vue from 'vue';
import './cursor.js';

Vue.directive('clickout', {
  bind(el, binding) {
    const handler = (e) => {
      if (el.contains(e.target)) {
        return;
      }
      if (binding.expression) {
        typeof binding.value === 'function' && binding.value();
      }
    };
    el.handler = handler;
    window.addEventListener('click', el.handler);
  },
  unbind(el) {
    window.removeEventListener('click', el.handler);
  },
});
Vue.directive('focus', {
  inserted(el) {
    const dom = el.querySelector('textarea,input');
    if (['textarea', 'input'].includes(el.tagName)) {
      el.focus();
    } else if (dom) {
      dom.focus();
    }
  },
});
