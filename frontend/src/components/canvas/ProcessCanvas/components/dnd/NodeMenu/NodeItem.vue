
<template>
  <div
    class="entry-item"
    :data-config-atom-id="type === 'task' ? node.code : node.template_id"
    :data-config-tpl-source="node.tplSource"
    :data-config-name="node.name.replace(/\s/g, '')"
    :data-config-api-meta="node.apiMeta"
    :data-config-group="node.group_name"
    :data-config-icon="node.group_icon"
    :data-type="type">
    <div
      v-if="node.plugin_type"
      class="plugin-item">
      <img
        class="plugin-logo"
        :src="node.logo_url"
        alt="">
      <div>
        <p
          v-bk-overflow-tips
          class="plugin-title name-wrapper">
          {{ node.nodeName }}
        </p>
        <p
          v-bk-overflow-tips="{ placement: 'bottom-end', extCls: 'plugin-code-tips', width: 300 }"
          class="plugin-desc">
          {{ node.desc || '--' }}
        </p>
      </div>
    </div>
    <div
      v-else
      v-bk-overflow-tips
      class="name-wrapper">
      {{ node.name }}
    </div>
  </div>
</template>
<script>

  export default {
    name: 'NodeItem',
    props: {
      type: {
        type: String,
        default: '',
      },
      node: {
        type: Object,
        default() {
          return {};
        },
      },
    },
  };
</script>
<style lang="scss" scoped>
  .plugin-item {
    display: flex;
    align-items: center;
    padding: 0 7px 0 13px;
    height: 70px;
    background: #fff;
    border-bottom: 1px solid #e2e4ed;
    color: #63656e;
    overflow: hidden;
    cursor: move;
    pointer-events: none;
    .plugin-logo {
      flex-shrink: 0;
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }
    .plugin-title {
      font-size: 14px;
      font-weight: 700;
      line-height: 19px;
      margin-bottom: 4px;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .plugin-desc {
      font-size: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
    &:hover {
      background: hsl(218, 100%, 94%);
    }
  }
  .name-wrapper {
    pointer-events: none;
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
</style>
<style lang="scss">
  .plugin-code-tips {
    .tippy-arrow {
      left: 270px !important;
    }
  }
</style>
