{"presets": [["@babel/preset-env", {"modules": false, "corejs": 3, "spec": true, "targets": {"browsers": ["> 1%", "last 2 versions", "not ie <= 8"]}, "useBuiltIns": "usage", "debug": false}], "@vue/babel-preset-jsx"], "plugins": ["@babel/plugin-transform-runtime", "@babel/plugin-proposal-object-rest-spread", ["component", {"libraryName": "element-ui", "styleLibraryName": "theme-chalk"}], "@babel/plugin-syntax-dynamic-import", "@babel/plugin-syntax-import-meta", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-json-strings", ["@babel/plugin-proposal-decorators", {"legacy": true}], "@babel/plugin-proposal-function-sent", "@babel/plugin-proposal-export-namespace-from", "@babel/plugin-proposal-numeric-separator", "@babel/plugin-proposal-throw-expressions"], "env": {"test": {"presets": [["@babel/preset-env", {"modules": "commonjs", "targets": {"node": "current"}}]]}}}