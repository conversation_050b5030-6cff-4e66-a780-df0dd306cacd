{"name": "bkflow_frontend", "version": "1.2.2", "description": "bkflow frontend project", "author": "bk", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server --progress --config ./build/webpack.dev.conf.js", "build": "cross-env npm run build:production", "build:development": "cross-env DEV_VAR=development webpack --config ./build/webpack.prod.conf.js", "build:production": "cross-env DEV_VAR=production webpack --config ./build/webpack.prod.conf.js", "lint": "eslint --ext .js,.vue src/", "fix": "eslint --fix --ext .js,.vue src/"}, "dependencies": {"@antv/x6": "^2.16.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-history": "^2.2.3", "@antv/x6-plugin-minimap": "^2.0.5", "@antv/x6-plugin-selection": "^2.1.7", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-vue-shape": "^2.0.11", "@babel/core": "^7.14.3", "@blueking/bkcharts": "^2.0.6", "@blueking/bkui-form": "0.0.42-beta.11", "@blueking/login-modal": "^1.0.1", "@blueking/notice-component-vue2": "^2.0.1", "@blueking/platform-config": "^1.0.3", "@blueking/user-selector": "^1.0.5-beta.2", "ajv": "^6.10.2", "axios": "^1.11.0", "bk-magic-vue": "2.5.8", "element-ui": "^2.15.13", "jquery": "^3.2.1", "js-cookie": "^3.0.1", "jsencrypt": "2.3.0", "lodash": "^4.17.12", "marked": "^4.2.2", "monaco-editor": "^0.50.0", "monaco-editor-webpack-plugin": "^7.1.0", "parse-svg-path": "^0.1.2", "sortablejs": "^1.15.6", "vee-validate": "^2.2.15", "vue": "2.7.14", "vue-i18n": "^7.6.0", "vue-router": "^2.8.1", "vuedraggable": "^2.24.3", "vuex": "^2.4.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "xss": "^1.0.15"}, "devDependencies": {"@babel/core": "^7.4.5", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/plugin-proposal-function-sent": "^7.0.0", "@babel/plugin-proposal-json-strings": "^7.0.0", "@babel/plugin-proposal-numeric-separator": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-import-meta": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.0.0", "@babel/plugin-transform-runtime": "^7.14.3", "@babel/preset-env": "^7.0.0", "@babel/runtime": "^7.7.6", "@vue/babel-preset-jsx": "^1.4.0", "autoprefixer": "^7.1.4", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^8.2.6", "babel-loader": "^8.0.0", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "^2.3.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "case-sensitive-paths-webpack-plugin": "^2.4.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0 ", "core-js": "^3.10.2", "cross-env": "^5.0.5", "css-loader": "^6.10.0", "dotenv": "^16.5.0", "dotenv-webpack": "^8.1.0", "eslint": "^5.16.0", "eslint-config-standard": "12.0.0", "eslint-config-tencent": "^1.0.4", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "eslint-plugin-vue": "5.2.2", "expose-loader": "^5.0.0", "file-loader": "^6.2.0", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^5.6.3", "lodash-webpack-plugin": "^0.11.4", "mini-css-extract-plugin": "^2.8.1", "moment": "^2.29.4", "moment-timezone": "^0.5.42", "optimize-css-assets-webpack-plugin": "^6.0.1", "postcss": "^8.4.35", "postcss-loader": "^8.1.1", "postcss-preset-env": "^9.5.2", "sass": "^1.55.0", "sass-loader": "^10.5.2", "style-loader": "^3.3.4", "terser-webpack-plugin": "^5.3.10", "url-loader": "^4.1.1", "vue-eslint-parser": "^7.11.0", "vue-json-pretty": "1.8.3", "vue-loader": "^15.11.1", "webpack": "^5.91.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-merge": "^5.10.0"}, "engines": {"node": ">= 18.20.4", "npm": ">= 10.7.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}