"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2024-03-18 07:55

from django.db import migrations, models

from bkflow.space.configs import SpaceConfigValueType, SpacePluginConfig


def reverse_func(apps, schema_editor):
    """delete all space plugin config"""
    SpaceConfig = apps.get_model("space", "SpaceConfig")
    db_alias = schema_editor.connection.alias
    SpaceConfig.objects.using(db_alias).filter(name=SpacePluginConfig.name).delete()


def forward_func(apps, schema_editor):
    """migrate space plugin config to space config"""
    SpacePluginConfigModel = apps.get_model("plugin", "SpacePluginConfig")
    SpaceConfig = apps.get_model("space", "SpaceConfig")
    db_alias = schema_editor.connection.alias

    existing_plugin_configs = SpacePluginConfigModel.objects.using(db_alias).all().order_by("-id")
    space_configs = []

    # only remain the last one
    space_id_set = set()
    for plugin_config in existing_plugin_configs:
        if plugin_config.space_id in space_id_set:
            continue
        space_configs.append(
            SpaceConfig(
                name=SpacePluginConfig.name,
                value_type=SpaceConfigValueType.JSON.value,
                space_id=plugin_config.space_id,
                json_value=plugin_config.config,
            )
        )
        space_id_set.add(plugin_config.space_id)

    if space_configs:
        print(f"copy {len(space_configs)} space plugin configs into space config table")
        SpaceConfig.objects.using(db_alias).bulk_create(space_configs)


class Migration(migrations.Migration):

    dependencies = [
        ("space", "0004_alter_spaceconfig_json_value"),
    ]

    operations = [
        migrations.AlterField(
            model_name="spaceconfig",
            name="name",
            field=models.CharField(
                choices=[
                    ("token_expiration", "Token过期时间"),
                    ("token_auto_renewal", "是否开启Token自动续期"),
                    ("callback_hooks", "回调配置"),
                    ("uniform_api", "是否开启统一API"),
                    ("superusers", "空间管理员"),
                    ("canvas_mode", "画布模式"),
                    ("gateway_expression", "网关表达式"),
                    ("api_gateway_credential_name", "API_GATEWAY使用的凭证名称"),
                    ("space_plugin_config", "空间插件配置"),
                ],
                max_length=32,
                verbose_name="配置项",
            ),
        ),
        migrations.RunPython(forward_func, reverse_func),
    ]
