# Generated by Django 3.2.25 on 2025-07-29 02:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('space', '0007_alter_space_app_code'),
    ]

    operations = [
        migrations.AlterField(
            model_name='spaceconfig',
            name='name',
            field=models.CharField(choices=[('token_expiration', 'Token过期时间'), ('token_auto_renewal', '是否开启Token自动续期'), ('engine_space_config', '引擎模块配置'), ('callback_hooks', '回调配置'), ('uniform_api', 'API 插件配置 （如更改配置，可能对已存在数据产生不兼容影响，请谨慎操作）'), ('superusers', '空间管理员'), ('canvas_mode', '画布模式'), ('gateway_expression', '网关表达式'), ('api_gateway_credential_name', 'API_GATEWAY使用的凭证配置'), ('space_plugin_config', '空间插件配置')], max_length=32, verbose_name='配置项'),
        ),
        migrations.AlterField(
            model_name='spaceconfig',
            name='value_type',
            field=models.CharField(choices=[('JSON', 'JSON'), ('TEXT', '文本'), ('REF', '引用')], default='TEXT', max_length=32, verbose_name='配置类型'),
        ),
    ]
