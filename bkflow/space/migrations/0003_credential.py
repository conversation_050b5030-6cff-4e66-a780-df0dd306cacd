"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-05-04 11:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("space", "0002_spaceconfig"),
    ]

    operations = [
        migrations.CreateModel(
            name="Credential",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("creator", models.CharField(blank=True, max_length=32, null=True, verbose_name="创建人")),
                ("create_at", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("update_at", models.DateTimeField(auto_now=True, verbose_name="更新时间")),
                ("updated_by", models.CharField(blank=True, max_length=32, null=True, verbose_name="修改人")),
                ("is_deleted", models.BooleanField(db_index=True, default=False, verbose_name="是否软删除")),
                ("space_id", models.IntegerField(verbose_name="空间ID")),
                ("name", models.CharField(max_length=32, verbose_name="凭证名")),
                ("desc", models.CharField(blank=True, max_length=128, null=True, verbose_name="凭证描述")),
                (
                    "type",
                    models.CharField(choices=[("BK_APP", "蓝鲸应用凭证")], max_length=32, verbose_name="凭证类型"),
                ),
                ("content", models.JSONField(blank=True, default=dict, null=True, verbose_name="凭证内容")),
            ],
            options={
                "verbose_name": "空间凭证",
                "verbose_name_plural": "空间凭证表",
                "unique_together": {("space_id", "name")},
            },
        ),
    ]
