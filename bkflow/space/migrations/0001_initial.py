"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-03-27 08:52

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Space",
            fields=[
                ("creator", models.CharField(blank=True, max_length=32, null=True, verbose_name="创建人")),
                ("create_at", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("update_at", models.DateTimeField(auto_now=True, verbose_name="更新时间")),
                ("updated_by", models.CharField(blank=True, max_length=32, null=True, verbose_name="修改人")),
                ("is_deleted", models.BooleanField(db_index=True, default=False, verbose_name="是否软删除")),
                ("id", models.AutoField(primary_key=True, serialize=False, verbose_name="空间ID")),
                ("name", models.CharField(max_length=32, unique=True, verbose_name="空间名称")),
                ("app_code", models.CharField(max_length=32, verbose_name="APP ID")),
                ("desc", models.CharField(blank=True, max_length=128, null=True, verbose_name="空间描述")),
                ("platform_url", models.CharField(max_length=256, verbose_name="平台提供服务的地址")),
                (
                    "create_type",
                    models.CharField(
                        choices=[("API", "API"), ("WEB", "WEB")],
                        default="API",
                        max_length=32,
                        verbose_name="空间创建的方式",
                    ),
                ),
            ],
            options={
                "verbose_name": "空间信息",
                "verbose_name_plural": "空间信息表",
            },
        )
    ]
