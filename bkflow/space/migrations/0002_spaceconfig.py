"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-04-30 07:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("space", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="SpaceConfig",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("space_id", models.IntegerField(verbose_name="空间ID")),
                (
                    "value_type",
                    models.CharField(
                        choices=[("JSON", "JSON"), ("TEXT", "文本")],
                        default="TEXT",
                        max_length=32,
                        verbose_name="配置类型",
                    ),
                ),
                ("name", models.CharField(max_length=32, verbose_name="配置项")),
                ("text_value", models.CharField(default="", max_length=128, verbose_name="配置值")),
                ("json_value", models.JSONField(default=dict, verbose_name="配置值(JSON)")),
            ],
            options={
                "verbose_name": "空间配置",
                "verbose_name_plural": "空间配置表",
                "unique_together": {("space_id", "name")},
            },
        ),
    ]
