"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2025-04-02 02:14

from django.db import migrations, models

import bkflow.bk_plugin.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="BKPlugin",
            fields=[
                ("code", models.CharField(max_length=100, primary_key=True, serialize=False, verbose_name="插件code")),
                ("name", models.CharField(max_length=255, verbose_name="插件名称")),
                ("tag", models.IntegerField(db_index=True, verbose_name="插件隶属分类")),
                ("logo_url", models.CharField(max_length=255, verbose_name="插件图片url")),
                ("created_time", models.CharField(blank=True, max_length=255, null=True, verbose_name="插件创建时间")),
                ("updated_time", models.CharField(blank=True, max_length=255, null=True, verbose_name="插件更新时间")),
                ("introduction", models.CharField(max_length=255, verbose_name="插件简介")),
                ("managers", models.JSONField(default=list, verbose_name="插件管理员列表")),
                ("extra_info", models.JSONField(default=dict, verbose_name="额外信息")),
            ],
            options={
                "verbose_name": "蓝鲸插件",
                "verbose_name_plural": "蓝鲸插件",
            },
        ),
        migrations.CreateModel(
            name="BKPluginAuthorization",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("code", models.CharField(db_index=True, max_length=100, verbose_name="插件code")),
                (
                    "status",
                    models.IntegerField(
                        choices=[
                            (bkflow.bk_plugin.models.AuthStatus["authorized"], "已授权"),
                            (bkflow.bk_plugin.models.AuthStatus["unauthorized"], "未授权"),
                        ],
                        default=bkflow.bk_plugin.models.AuthStatus["unauthorized"],
                        verbose_name="授权状态",
                    ),
                ),
                ("status_update_time", models.DateTimeField(blank=True, null=True, verbose_name="最近一次授权操作时间")),
                (
                    "config",
                    models.JSONField(default=bkflow.bk_plugin.models.get_default_config, verbose_name="授权配置，如使用范围等"),
                ),
                (
                    "status_updator",
                    models.CharField(blank=True, default="", max_length=100, verbose_name="最近一次授权操作的人员名称"),
                ),
            ],
            options={
                "verbose_name": "蓝鲸插件授权记录",
                "verbose_name_plural": "蓝鲸插件授权记录",
            },
        ),
    ]
