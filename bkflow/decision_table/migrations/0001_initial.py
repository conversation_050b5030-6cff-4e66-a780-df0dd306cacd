"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2024-04-02 09:16

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="DecisionTable",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("creator", models.CharField(blank=True, max_length=32, null=True, verbose_name="创建人")),
                ("create_at", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("update_at", models.DateTimeField(auto_now=True, verbose_name="更新时间")),
                ("updated_by", models.CharField(blank=True, max_length=32, null=True, verbose_name="修改人")),
                ("is_deleted", models.BooleanField(db_index=True, default=False, verbose_name="是否软删除")),
                ("name", models.CharField(max_length=64, verbose_name="decision table name")),
                ("space_id", models.IntegerField(db_index=True, verbose_name="space ID")),
                (
                    "template_id",
                    models.BigIntegerField(blank=True, db_index=True, null=True, verbose_name="template ID"),
                ),
                (
                    "scope_type",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="scope type of decision table"
                    ),
                ),
                (
                    "scope_value",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="scope value of decision table"
                    ),
                ),
                ("data", models.JSONField(verbose_name="data of decision table")),
                (
                    "table_type",
                    models.CharField(
                        choices=[("single", "single"), ("multi", "multi")],
                        default="single",
                        max_length=32,
                        verbose_name="table type",
                    ),
                ),
                ("extra_info", models.JSONField(default=dict, verbose_name="extra info")),
            ],
            options={
                "verbose_name": "Decision Table",
                "ordering": ["-id"],
                "index_together": {("space_id", "scope_type", "scope_value")},
            },
        ),
    ]
