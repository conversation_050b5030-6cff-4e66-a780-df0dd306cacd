"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-04-24 07:23

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="ModuleInfo",
            fields=[
                ("id", models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("space_id", models.IntegerField(verbose_name="空间ID")),
                ("code", models.CharField(max_length=32, verbose_name="模块code")),
                ("url", models.CharField(max_length=512, verbose_name="模块提供的地址")),
                ("token", models.CharField(max_length=32, verbose_name="模块的token")),
                ("type", models.CharField(choices=[("TASK", "任务模块")], max_length=32, verbose_name="模块类型")),
                (
                    "isolation_level",
                    models.CharField(
                        choices=[("only_calculation", "仅隔离计算"), ("all_resource", "全部隔离")],
                        max_length=32,
                        verbose_name="隔离类型",
                    ),
                ),
            ],
            options={
                "verbose_name": "模块信息表",
                "verbose_name_plural": "模块信息表",
            },
        ),
    ]
