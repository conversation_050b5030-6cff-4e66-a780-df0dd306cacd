### 资源描述

获取任务节点详情

### 输入通用参数说明
| 参数名称          | 参数类型   | 必须 | 参数说明                                                       |
|---------------|--------|----|------------------------------------------------------------|
| bk_app_code   | string | 是  | 应用ID(app id)，可以通过 蓝鲸开发者中心 -> 应用基本设置 -> 基本信息 -> 鉴权信息 获取     |
| bk_app_secret | string | 是  | 安全秘钥(app secret)，可以通过 蓝鲸开发者中心 -> 应用基本设置 -> 基本信息 -> 鉴权信息 获取 |

#### 接口参数

| 字段             | 类型      | 必选 | 描述                                 |
|----------------|---------|----|------------------------------------|
| loop           | int     | 否  | 获取节点第 loop 次循环的数据，默认为最新一次的数据       |
| component_code | string  | 否  | 节点对应的插件 code，用于进行节点输出的格式化，默认不进行格式化 |

### 返回结果示例

```json
{
    "result": true,
    "data": {
        "name": "定时",
        "error_ignorable": false,
        "state": "READY",
        "inputs": {
            "bk_timing": "30",
            "force_check": true
        },
        "outputs": [],
        "ex_data": ""
    },
    "message": ""
}
```

### 返回结果参数说明

| 字段      | 类型     | 描述                    |
|---------|--------|-----------------------|
| result  | bool   | 返回结果，true为成功，false为失败 |
| code    | int    | 返回码，0表示成功，其他值表示失败     |
| message | string | 错误信息                  |
| data    | dict   | 返回数据                  |

#### data 字段说明

| 字段              | 类型     | 描述           |
|-----------------|--------|--------------|
| name            | string | 节点名称         |
| error_ignorable | bool   | 节点失败是否可忽略    |
| state           | string | 节点状态         |
| inputs          | dict   | 节点输入参数       |
| outputs         | list   | 节点输出参数       |
| ex_data         | string | 节点执行失败时的错误信息 |
