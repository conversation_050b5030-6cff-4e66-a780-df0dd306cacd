swagger: '2.0'
basePath: /
info:
  version: '0.1'
  title: API Gateway Resources
  description: ''
schemes:
- http
paths:
  /create_space:
    post:
      operationId: create_space
      description: 创建空间
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/create_space/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Create a space
  /space/{space_id}/apply_token/:
    post:
      operationId: apply_token
      description: 申请token
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/apply_token/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: true
        disabledStages: []
        descriptionEn: Apply a token
  /space/{space_id}/revoke_token/:
    post:
      operationId: revoke_token
      description: 撤回 token
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/revoke_token/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Revoke a token
  /space/{space_id}/create_task/:
    post:
      operationId: create_task
      description: 创建任务
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/create_task/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Create a task
  /space/{space_id}/create_task_without_template/:
    post:
      operationId: create_task_without_template
      description: 创建无模版任务
      tags: [ ]
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/create_task_without_template/
          matchSubpath: false
          timeout: 0
          upstreams: { }
          transformHeaders: { }
        authConfig:
          userVerifiedRequired: false
        disabledStages: [ ]
        descriptionEn: Create a task without template
  /space/{space_id}/validate_pipeline_tree/:
    post:
      operationId: validate_pipeline_tree
      description: 校验 pipeline_tree 是否合法
      tags: [ ]
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/validate_pipeline_tree/
          matchSubpath: false
          timeout: 0
          upstreams: { }
          transformHeaders: { }
        authConfig:
          userVerifiedRequired: false
        disabledStages: [ ]
        descriptionEn: Validate a pipeline tree
  /space/{space_id}/create_mock_task/:
    post:
      operationId: create_mock_task
      description: 创建 mock 任务
      tags: [ ]
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/create_mock_task/
          matchSubpath: false
          timeout: 0
          upstreams: { }
          transformHeaders: { }
        authConfig:
          userVerifiedRequired: false
        disabledStages: [ ]
        descriptionEn: Create a mock task
  /space/{space_id}/create_template/:
    post:
      operationId: create_template
      description: 创建模版
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/create_template/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: create a template
  /space/{space_id}/get_space_configs/:
    get:
      operationId: get_space_configs
      description: 获取空间配置
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: get
          path: /{env.api_sub_path}apigw/space/{space_id}/get_space_configs/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Get configs of a space
  /space/{space_id}/get_task_list/:
    get:
      operationId: get_task_list
      description: 获取任务列表
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: get
          path: /{env.api_sub_path}apigw/space/{space_id}/get_task_list/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Get task list
  /space/{space_id}/renew_space_config/:
    post:
      operationId: renew_space_config
      description: 刷新空间配置
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/renew_space_config/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Renew config of a space
  /space/{space_id}/task/{task_id}/get_task_detail/:
    get:
      operationId: get_task_detail
      description: 获取任务详情
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: get
          path: /{env.api_sub_path}apigw/space/{space_id}/task/{task_id}/get_task_detail/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Get detail of a task
  /space/{space_id}/task/{task_id}/node/{node_id}/get_task_node_detail/:
    get:
      operationId: get_task_node_detail
      description: 获取任务节点详情
      tags: [ ]
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: get
          path: /{env.api_sub_path}apigw/space/{space_id}/task/{task_id}/node/{node_id}/get_task_node_detail/
          matchSubpath: false
          timeout: 0
          upstreams: { }
          transformHeaders: { }
        authConfig:
          userVerifiedRequired: false
        disabledStages: [ ]
        descriptionEn: Get node detail of a task
  /space/{space_id}/task/{task_id}/get_task_states/:
    get:
      operationId: get_task_states
      description: 获取任务状态
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: get
          path: /{env.api_sub_path}apigw/space/{space_id}/task/{task_id}/get_task_states/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Get states of a task
  /space/{space_id}/get_tasks_states/:
    post:
      operationId: get_tasks_states
      description: 批量获取任务状态
      tags: [ ]
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/get_tasks_states/
          matchSubpath: false
          timeout: 0
          upstreams: { }
          transformHeaders: { }
        authConfig:
          userVerifiedRequired: false
        disabledStages: [ ]
        descriptionEn: Get states of a task with filter params
  /space/{space_id}/task/{task_id}/operate_task/{operation}/:
    post:
      operationId: operate_task
      description: 操作任务
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/task/{task_id}/operate_task/{operation}/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Operate task
  /space/{space_id}/task/{task_id}/node/{node_id}/operate_node/{operation}/:
    post:
      operationId: operate_task_node
      description: 操作任务节点
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/task/{task_id}/node/{node_id}/operate_node/{operation}/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Operate task node
  /space/{space_id}/template/{template_id}/get_template_detail/:
    get:
      operationId: get_template_detail
      description: 获取模版详情
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: get
          path: /{env.api_sub_path}apigw/space/{space_id}/template/{template_id}/get_template_detail/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Get detail of a template
  /space/{space_id}/template/{template_id}/get_template_mock_data/:
    get:
      operationId: get_template_mock_data
      description: 获取模版 mock 数据
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: get
          path: /{env.api_sub_path}apigw/space/{space_id}/template/{template_id}/get_template_mock_data/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Get mock data of a template
  /space/{space_id}/get_template_list/:
    get:
      operationId: get_template_list
      description: 获取模版列表
      tags: [ ]
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: get
          path: /{env.api_sub_path}apigw/space/{space_id}/get_template_list/
          matchSubpath: false
          timeout: 0
          upstreams: { }
          transformHeaders: { }
        authConfig:
          userVerifiedRequired: false
        disabledStages: [ ]
        descriptionEn: Get list of templates
  /space/{space_id}/apply_webhook_configs/:
    post:
      operationId: apply_webhook_configs
      description: 应用空间 webhook 配置
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/apply_webhook_configs/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: Apply configs of webhook
  /space/{space_id}/update_template/{template_id}/:
    post:
      operationId: update_template
      description: 更新模板
      tags: [ ]
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/update_template/{template_id}/
          matchSubpath: false
          timeout: 0
          upstreams: { }
          transformHeaders: { }
        authConfig:
          userVerifiedRequired: false
        disabledStages: [ ]
        descriptionEn: update a template
  /space/{space_id}/delete_template/{template_id}/:
    post:
      operationId: delete_template
      description: 删除模板
      tags: [ ]
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/delete_template/{template_id}/
          matchSubpath: false
          timeout: 0
          upstreams: { }
          transformHeaders: { }
        authConfig:
          userVerifiedRequired: false
        disabledStages: [ ]
        descriptionEn: delete a template
  /space/{space_id}/create_credential/:
    post:
      operationId: create_credential
      description: 创建凭证
      tags: [ ]
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: true
        allowApplyPermission: true
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/space/{space_id}/create_credential/
          matchSubpath: false
          timeout: 0
          upstreams: { }
          transformHeaders: { }
        authConfig:
          userVerifiedRequired: false
        disabledStages: [ ]
        descriptionEn: create a credential
  /grant_apigw_permissions_to_app:
    post:
      operationId: grant_apigw_permissions_to_app
      description: 给其他 app 授予 apigw 权限
      tags: []
      responses:
        default:
          description: ''
      x-bk-apigateway-resource:
        isPublic: false
        allowApplyPermission: false
        matchSubpath: false
        backend:
          type: HTTP
          method: post
          path: /{env.api_sub_path}apigw/grant_apigw_permissions_to_app/
          matchSubpath: false
          timeout: 0
          upstreams: {}
          transformHeaders: {}
        authConfig:
          userVerifiedRequired: false
        disabledStages: []
        descriptionEn: grant apigw permissions of bkflow to other app