apigateway:
  description: {{ settings.BK_APP_CODE }} apigw
  is_public: true
  maintainers:
    {% for member in settings.BK_APIGW_MANAGER_MAINTAINERS %}
    - "{{ member }}"
    {% endfor %}

stage:
  name: {{ settings.BK_APIGW_STAGE_NAME }}
  vars:
    api_sub_path: "{{ settings.BK_APIGW_API_SERVER_SUB_PATH }}"
  proxy_http:
    timeout: 120
    upstreams:
      loadbalance: roundrobin
      hosts:
        - host: http://{{ settings.BK_APIGW_API_SERVER_HOST }}
          weight: 100
  rate_limit:
    enabled: false
    rate:
      tokens: 5000
      period: 60

grant_permissions:
  {% for app_code in settings.BK_APIGW_GRANT_APPS %}
  - bk_app_code: "{{ app_code }}"
    grant_dimension: "api"
  {% endfor %}


resource_docs:
  archivefile: {{ settings.BK_APIGW_RESOURCE_DOCS_ARCHIVE_FILE }}

release:
  version: {{ settings.STATIC_VERSION }}+{{ settings.DEPLOY_DATETIME }}
  title: "bkflow release"
  comment: "auto release by bkflow"
