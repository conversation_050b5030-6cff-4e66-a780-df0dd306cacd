"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-03-23 09:10

import pipeline.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="TemplateSnapshot",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False, verbose_name="快照ID")),
                ("md5sum", models.CharField(db_index=True, max_length=32, verbose_name="快照字符串的md5sum")),
                ("create_time", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("data", pipeline.models.CompressJSONField(blank=True, help_text="存储的数据", null=True)),
                ("template_id", models.IntegerField(help_text="模板ID", null=True)),
            ],
            options={
                "verbose_name": "模板快照",
                "verbose_name_plural": "模板快照表",
                "ordering": ["-id"],
            },
        ),
        migrations.CreateModel(
            name="Template",
            fields=[
                ("creator", models.CharField(blank=True, max_length=32, null=True, verbose_name="创建人")),
                ("create_at", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("update_at", models.DateTimeField(auto_now=True, verbose_name="更新时间")),
                ("updated_by", models.CharField(blank=True, max_length=32, null=True, verbose_name="修改人")),
                ("is_deleted", models.BooleanField(db_index=True, default=False, verbose_name="是否软删除")),
                ("id", models.BigAutoField(primary_key=True, serialize=False, verbose_name="模版ID")),
                ("space_id", models.IntegerField(verbose_name="空间ID")),
                ("snapshot_id", models.BigIntegerField(verbose_name="模板对应的数据ID")),
                ("name", models.CharField(max_length=128, verbose_name="模版名称")),
                ("desc", models.CharField(blank=True, max_length=256, null=True, verbose_name="描述")),
                ("notify_config", models.JSONField(default=dict, verbose_name="流程事件通知配置")),
                ("scope_type", models.CharField(blank=True, max_length=128, null=True, verbose_name="流程范围类型")),
                ("scope_value", models.CharField(blank=True, max_length=128, null=True, verbose_name="流程范围")),
                (
                    "source",
                    models.CharField(
                        blank=True, help_text="第三方系统对应的资源ID", max_length=32, null=True, verbose_name="来源"
                    ),
                ),
                ("version", models.CharField(max_length=32, verbose_name="版本号")),
                ("is_enabled", models.BooleanField(default=True, verbose_name="是否启用")),
                ("extra_info", models.JSONField(default=dict, verbose_name="额外的扩展信息")),
            ],
            options={
                "verbose_name": "流程模板",
                "verbose_name_plural": "流程模板信息表",
                "index_together": {("space_id", "scope_type", "scope_value")},
            },
        ),
    ]
