"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-05-18 05:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("template", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="TemplateOperationRecord",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False, verbose_name="ID")),
                ("operator", models.CharField(max_length=128, verbose_name="操作人")),
                ("instance_id", models.IntegerField(verbose_name="记录对象实例ID")),
                ("operate_date", models.DateTimeField(auto_now_add=True, verbose_name="操作时间")),
                ("extra_info", models.JSONField(blank=True, default=dict, null=True, verbose_name="额外信息")),
                (
                    "operate_type",
                    models.CharField(
                        choices=[("create", "创建"), ("delete", "删除"), ("update", "修改")],
                        max_length=64,
                        verbose_name="操作类型",
                    ),
                ),
                (
                    "operate_source",
                    models.CharField(
                        choices=[("app", "app 页面"), ("api", "api 接口")], max_length=64, verbose_name="操作来源"
                    ),
                ),
            ],
            options={
                "verbose_name": "模版操作记录",
                "verbose_name_plural": "模版操作记录",
            },
        ),
        migrations.AddIndex(
            model_name="templateoperationrecord",
            index=models.Index(fields=["instance_id"], name="template_te_instanc_61b855_idx"),
        ),
    ]
