"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-04-20 11:49

import pipeline.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("task", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="TaskExecutionSnapshot",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False, verbose_name="快照ID")),
                ("md5sum", models.CharField(db_index=True, max_length=32, verbose_name="快照字符串的md5sum")),
                ("create_time", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("data", pipeline.models.CompressJSONField(blank=True, help_text="存储的数据", null=True)),
            ],
            options={
                "verbose_name": "任务执行快照",
                "verbose_name_plural": "任务执行快照",
                "ordering": ["-id"],
            },
        ),
        migrations.AlterField(
            model_name="tasksnapshot",
            name="data",
            field=pipeline.models.CompressJSONField(blank=True, help_text="存储的数据", null=True),
        ),
    ]
