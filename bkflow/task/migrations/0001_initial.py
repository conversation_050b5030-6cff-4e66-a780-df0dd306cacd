"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-04-03 06:22

import pipeline.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="TaskSnapshot",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False, verbose_name="快照ID")),
                ("md5sum", models.CharField(db_index=True, max_length=32, verbose_name="快照字符串的md5sum")),
                ("create_time", models.DateTimeField(auto_now_add=True, verbose_name="创建时间")),
                ("data", pipeline.models.CompressJSONField(blank=True, help_text="数据", null=True)),
            ],
            options={
                "verbose_name": "模板快照",
                "verbose_name_plural": "模板快照",
                "ordering": ["-id"],
            },
        ),
        migrations.CreateModel(
            name="TaskTreeInfo",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("data", pipeline.models.CompressJSONField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="TaskInstance",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("space_id", models.IntegerField(db_index=True, verbose_name="空间ID")),
                ("scope_type", models.CharField(blank=True, max_length=128, null=True, verbose_name="空间域类型")),
                ("scope_value", models.CharField(blank=True, max_length=128, null=True, verbose_name="空间域值")),
                ("instance_id", models.CharField(db_index=True, max_length=33, unique=True, verbose_name="实例ID")),
                (
                    "template_id",
                    models.BigIntegerField(blank=True, db_index=True, null=True, verbose_name="流程模版ID"),
                ),
                (
                    "name",
                    models.CharField(default="default_taskflow_instance", max_length=128, verbose_name="实例名称"),
                ),
                ("creator", models.CharField(blank=True, max_length=32, verbose_name="创建者")),
                ("create_time", models.DateTimeField(auto_now_add=True, db_index=True, verbose_name="创建时间")),
                ("executor", models.CharField(blank=True, max_length=32, verbose_name="执行者")),
                ("start_time", models.DateTimeField(blank=True, null=True, verbose_name="启动时间")),
                ("finish_time", models.DateTimeField(blank=True, null=True, verbose_name="结束时间")),
                ("description", models.TextField(blank=True, verbose_name="描述")),
                ("is_started", models.BooleanField(default=False, verbose_name="是否已经启动")),
                ("is_finished", models.BooleanField(default=False, verbose_name="是否已经完成")),
                ("is_revoked", models.BooleanField(default=False, verbose_name="是否已经撤销")),
                (
                    "is_deleted",
                    models.BooleanField(default=False, help_text="表示当前实例是否删除", verbose_name="是否已经删除"),
                ),
                (
                    "is_expired",
                    models.BooleanField(
                        default=False, help_text="运行时被定期清理即为过期", verbose_name="是否已经过期"
                    ),
                ),
                (
                    "snapshot_id",
                    models.BigIntegerField(blank=True, db_index=True, null=True, verbose_name="实例结构数据ID"),
                ),
                (
                    "execution_snapshot_id",
                    models.BigIntegerField(
                        blank=True, db_index=True, null=True, verbose_name="用于实例执行的结构数据ID"
                    ),
                ),
                (
                    "tree_info_id",
                    models.BigIntegerField(
                        blank=True, db_index=True, null=True, verbose_name="提前计算好的一些流程结构数据ID"
                    ),
                ),
                ("extra_info", models.JSONField(default=dict, verbose_name="额外信息")),
            ],
            options={
                "verbose_name": "任务实例",
                "verbose_name_plural": "任务实例",
                "ordering": ["-create_time"],
                "index_together": {("space_id", "scope_type", "scope_value")},
            },
        ),
    ]
