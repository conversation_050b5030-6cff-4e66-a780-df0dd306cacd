"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-05-05 03:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("task", "0003_autoretrynodestrategy"),
    ]

    operations = [
        migrations.CreateModel(
            name="TimeoutNodesRecord",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False, verbose_name="ID")),
                ("timeout_nodes", models.TextField(verbose_name="超时节点信息")),
            ],
            options={
                "verbose_name": "超时节点数据记录 TimeoutNodesRecord",
                "verbose_name_plural": "超时节点数据记录 TimeoutNodesRecord",
            },
        ),
        migrations.CreateModel(
            name="TimeoutNodeConfig",
            fields=[
                ("task_id", models.BigIntegerField(verbose_name="taskflow id")),
                ("root_pipeline_id", models.CharField(max_length=64, verbose_name="root pipeline id")),
                (
                    "action",
                    models.CharField(
                        choices=[("forced_fail", "强制失败"), ("forced_fail_and_skip", "强制失败并跳过")],
                        max_length=32,
                        verbose_name="action",
                    ),
                ),
                (
                    "node_id",
                    models.CharField(max_length=64, primary_key=True, serialize=False, verbose_name="task node id"),
                ),
                ("timeout", models.IntegerField(verbose_name="node timeout time")),
            ],
            options={
                "verbose_name": "节点超时配置 TimeoutNodeConfig",
                "verbose_name_plural": "节点超时配置 TimeoutNodeConfig",
                "index_together": {("root_pipeline_id", "node_id")},
            },
        ),
    ]
