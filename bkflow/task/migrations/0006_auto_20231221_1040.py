"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-12-21 02:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("task", "0005_auto_20230516_2144"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="taskoperationrecord",
            options={
                "ordering": ["-id"],
                "verbose_name": "任务操作记录 TaskOperationRecord",
                "verbose_name_plural": "任务操作记录 TaskOperationRecord",
            },
        ),
        migrations.AddField(
            model_name="taskinstance",
            name="create_method",
            field=models.CharField(
                choices=[("API", "API"), ("MOCK", "MOCK")], default="API", max_length=32, verbose_name="创建方式"
            ),
        ),
        migrations.AlterField(
            model_name="taskoperationrecord",
            name="operate_source",
            field=models.CharField(
                choices=[("app", "app 页面"), ("api", "api 接口")], max_length=64, verbose_name="操作来源"
            ),
        ),
        migrations.AlterField(
            model_name="taskoperationrecord",
            name="operate_type",
            field=models.CharField(
                choices=[
                    ("create", "创建"),
                    ("delete", "删除"),
                    ("update", "修改"),
                    ("start", "执行"),
                    ("pause", "暂停"),
                    ("resume", "继续"),
                    ("revoke", "撤消"),
                    ("callback", "回调"),
                    ("retry", "重试"),
                    ("skip", "跳过"),
                    ("skip_exg", "跳过失败网关"),
                    ("skip_cpg", "跳过并行条件网关"),
                    ("pause_subproc", "暂停节点"),
                    ("resume_subproc", "继续节点"),
                    ("forced_fail", "强制失败"),
                    ("task_action", "任务操作"),
                    ("nodes_action", "节点操作"),
                ],
                max_length=64,
                verbose_name="操作类型",
            ),
        ),
    ]
