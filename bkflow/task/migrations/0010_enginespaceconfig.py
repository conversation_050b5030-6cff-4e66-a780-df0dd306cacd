# Generated by Django 3.2.15 on 2025-05-19 09:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("task", "0009_taskmockdata_mock_data_ids"),
    ]

    operations = [
        migrations.CreateModel(
            name="EngineSpaceConfig",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("interface_config_id", models.BigIntegerField(unique=True, verbose_name="交互模块配置id")),
                ("name", models.CharField(max_length=255, verbose_name="配置名称")),
                ("desc", models.TextField(blank=True, null=True, verbose_name="描述")),
                ("is_public", models.BooleanField(default=True, verbose_name="是否公开")),
                (
                    "value_type",
                    models.CharField(choices=[("TEXT", "文本"), ("<PERSON><PERSON><PERSON>", "JSO<PERSON>")], default="TEXT", max_length=10),
                ),
                ("is_mix_type", models.BooleanField(default=False, verbose_name="是否混合类型")),
                ("text_value", models.CharField(default="", max_length=128, verbose_name="配置值")),
                ("json_value", models.JSONField(blank=True, default=dict, verbose_name="配置值(JSON)")),
                ("space_id", models.BigIntegerField(verbose_name="空间id")),
            ],
        ),
    ]
