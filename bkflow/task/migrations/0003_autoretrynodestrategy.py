"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
# Generated by Django 3.2.15 on 2023-05-02 07:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("task", "0002_auto_20230420_1949"),
    ]

    operations = [
        migrations.CreateModel(
            name="AutoRetryNodeStrategy",
            fields=[
                ("taskflow_id", models.BigIntegerField(verbose_name="taskflow id")),
                ("root_pipeline_id", models.CharField(max_length=64, verbose_name="root pipeline id")),
                (
                    "node_id",
                    models.CharField(max_length=64, primary_key=True, serialize=False, verbose_name="task node id"),
                ),
                ("retry_times", models.IntegerField(default=0, verbose_name="retry times")),
                ("max_retry_times", models.IntegerField(default=5, verbose_name="retry times")),
                ("interval", models.IntegerField(default=0, verbose_name="retry interval")),
            ],
            options={
                "verbose_name": "节点自动重试策略 AutoRetryNodeStrategy",
                "verbose_name_plural": "节点自动重试策略 AutoRetryNodeStrategy",
                "index_together": {("root_pipeline_id", "node_id")},
            },
        ),
    ]
