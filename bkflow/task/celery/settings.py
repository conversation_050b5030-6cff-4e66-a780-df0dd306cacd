"""
TencentBlueKing is pleased to support the open source community by making
蓝鲸流程引擎服务 (BlueKing Flow Engine Service) available.
Copyright (C) 2024 THL A29 Limited,
a Tencent company. All rights reserved.
Licensed under the MIT License (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
either express or implied. See the License for the
specific language governing permissions and limitations under the License.

We undertake not to change the open source license (MIT license) applicable

to the current version of the project delivered to anyone in the future.
"""
from typing import List

from kombu import Exchange, Queue


def get_task_queues(module_code: str) -> list[Queue]:
    return [
        Queue(
            f"node_auto_retry_{module_code}",
            Exchange("default", type="direct"),
            routing_key=f"node_auto_retry_{module_code}",
            queue_arguments={"x-max-priority": 255},
        ),
        Queue(
            f"timeout_node_execute_{module_code}",
            Exchange("default", type="direct"),
            routing_key=f"timeout_node_execute_{module_code}",
            queue_arguments={"x-max-priority": 255},
        ),
        Queue(
            f"timeout_node_record_{module_code}",
            Exchange("default", type="direct"),
            routing_key=f"timeout_node_record_{module_code}",
            queue_arguments={"x-max-priority": 255},
        ),
        Queue(
            f"task_common_{module_code}",
            Exchange("default", type="direct"),
            routing_key=f"task_common_{module_code}",
            queue_arguments={"x-max-priority": 255},
        ),
        Queue(
            f"clean_task_{module_code}",
            Exchange("default", type="direct"),
            routing_key=f"clean_task_{module_code}",
            queue_arguments={"x-max-priority": 255},
        ),
    ]
