
(function () {
    $.atoms.select = [
        {
            tag_code: "select_meta",
            type: "combine",
            attrs: {
                name: gettext("下拉框"),
                hookable: true,
                children: [
                    {
                        tag_code: "datasource",
                        type: "radio",
                        attrs: {
                            name: gettext("数据源"),
                            hookable: true,
                            items: [{name: gettext("自定义"), value: "0"}, {name: gettext("远程数据源"), value: "1"}],
                            value: "0",
                            hidden: true,
                            validation: [
                                {
                                    type: "required"
                                }
                            ]
                        }
                    },
                    {
                        tag_code: "items_text",
                        type: "textarea",
                        attrs: {
                            name: gettext("选项"),
                            hookable: true,
                            placeholder: gettext('请输入数据源信息，自定义数据源格式为 [{"text": "", "value": ""}...]'),
                            validation: [
                                {
                                    type: "required"
                                }
                            ]
                        }
                    },
                    {
                        tag_code: "type",
                        type: "radio",
                        attrs: {
                            name: gettext("类型"),
                            hookable: true,
                            items: [{name: gettext("单选"), value: "0"}, {name: gettext("多选"), value: "1"}],
                            value: "0",
                            validation: [
                                {
                                    type: "required"
                                }
                            ]
                        }
                    },
                    {
                        tag_code: "default",
                        type: "textarea",
                        attrs: {
                            name: gettext("默认值"),
                            placeholder: gettext("请输入下拉框的默认值，单选为 value 的格式，多选为 value,value,... 的格式"),
                            hookable: true
                        }
                    }
                ]
            }

        },
        {
            tag_code: "select",
            meta_transform: function (variable) {
                let metaConfig = variable.value;
                let remote = false;
                let remote_url = "";
                let items = [];
                let placeholder = '';
                // if (metaConfig.datasource === "1") {
                //     remote_url = $.context.get('site_url') + 'api/plugin_query/variable_select_source_data_proxy/?url=' + metaConfig.items_text;
                //     remote = true;
                // }
                if (metaConfig.datasource === "0") {
                    try {
                        items = JSON.parse(metaConfig.items_text);
                    } catch (err) {
                        items = [];
                        placeholder = gettext('非法下拉框数据源，请检查您的配置');
                    }
                    if (!(items instanceof Array)) {
                        items = [];
                        placeholder = gettext('非法下拉框数据源，请检查您的配置');
                    }
                }

                let multiple = false;
                let default_val = metaConfig.default || '';

                // if (metaConfig.type === "1") {
                //     multiple = true;
                //     default_val = [];
                //     if (metaConfig.default) {
                //         let vals = metaConfig.default.split(',');
                //         for (let i in vals) {
                //             default_val.push(vals[i].trim());
                //         }
                //     }
                // }
                return {
                    tag_code: this.tag_code,
                    type: "select",
                    attrs: {
                        name: gettext("下拉框"),
                        hookable: true,
                        items: items,
                        multiple: multiple,
                        value: default_val,
                        remote: remote,
                        remote_url: remote_url,
                        placeholder: placeholder,
                        remote_data_init: function (data) {
                            return data;
                        },
                        validation: [
                            {
                                type: "required"
                            }
                        ]
                    }
                }
            }
        }
    ]
})();
