# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Flake8 and Black

on:
  push:
    branches: [ master, develop, release*, feature* ]
  pull_request:
    branches: [ master, develop, release*, feature* ]

jobs:
  flake8_and_black:
    runs-on: ubuntu-22.04

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python 3.9
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black
    - name: Lint with flake8
      run: |
        flake8
    - name: Format with black
      run: |
        black .