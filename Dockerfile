# -----------------------------------------------------------------------------
# Stage 1: Frontend Builder Stage
# -----------------------------------------------------------------------------
# 使用 Node.js 镜像作为前端构建环境
# 选择一个具体的 LTS 版本，例如 18-alpine，以获得更小、更快的构建体验
FROM node:18-alpine AS frontend-builder

LABEL maintainer="chiayzhang <<EMAIL>>"

WORKDIR /app

# 1. 拷贝 package.json 和 lock 文件，利用 Docker 缓存机制
#    只有当这些文件变化时，才会重新执行 npm install
COPY frontend/package*.json ./
RUN npm install --legacy-peer-deps

# 2. 拷贝所有前端源码
COPY frontend/ ./frontend

# 3. 执行构建命令
# RUN cd frontend && npm run build
RUN export NODE_OPTIONS=--max-old-space-size=4096 && npm run build --prefix frontend

# -----------------------------------------------------------------------------
# Stage 2: Python Builder Stage
# -----------------------------------------------------------------------------
FROM python:3.11-slim AS python-builder

LABEL maintainer="chiayzhang <<EMAIL>>"

ENV PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on

WORKDIR /app

# 安装编译时依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends build-essential && \
    rm -rf /var/lib/apt/lists/*

# 安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-warn-script-location -r requirements.txt

COPY . .
RUN mv ./bin/container_start.sh ./container_start.sh


# -----------------------------------------------------------------------------
# Stage 3: Final Production Stage
# -----------------------------------------------------------------------------
FROM python:3.11-slim

WORKDIR /app

# --- 创建非 root 用户 ---
# 以非 root 用户运行是安全最佳实践
# RUN addgroup --system app && adduser --system --group app

# --- 拷贝依赖和代码 ---
# 从 python-builder 阶段拷贝已安装的 Python 包
COPY --from=python-builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
# 从 python-builder 阶段拷贝 Python 可执行文件 (如 gunicorn)
COPY --from=python-builder /usr/local/bin /usr/local/bin
# 从 python-builder 阶段拷贝后端应用代码和脚本
COPY --from=python-builder /app .

# --- 拷贝前端构建产物 ---
# 从 frontend-builder 阶段拷贝编译好的前端静态文件
# 目标路径应为 Django `STATICFILES_DIRS` 配置可以找到的地方
# 这里我们假设将其放入 /app/src/static/frontend-dist
# 你需要在 settings.py 中配置 `STATICFILES_DIRS = [BASE_DIR / 'static']`
COPY --from=frontend-builder /app/frontend/static ./staticfiles/bkflow

# --- 准备启动脚本和权限 ---
RUN sed -i 's/\r$//' ./container_start.sh && \
    # 赋予执行权限
    chmod +x ./container_start.sh

# --- 声明环境变量 ---
# 启动脚本将负责验证这些变量是否已被注入。
# Django 和模块配置
ENV DJANGO_SETTINGS_MODULE="settings"
ENV PYTHONPATH="/app"
ENV BKFLOW_MODULE_TYPE="interface"
# 端口
ENV PORT="5000"

# --- 收集静态文件 (推荐) ---
# 这一步会将所有静态文件（包括前端产物）收集到 STATIC_ROOT
# 这使得 Nginx 或 WhiteNoise 等可以直接从一个目录提供服务
# 注意: 需要确保 DJANGO_SETTINGS_MODULE 和 PYTHONPATH 在此之前已设置
# RUN python src/manage.py collectstatic --noinput

# --- 设置工作目录所有权并切换用户 ---
# RUN chown -R app:app /app
# USER app

# 暴露端口
EXPOSE ${PORT}

# 定义容器启动命令
CMD ["./container_start.sh"]

