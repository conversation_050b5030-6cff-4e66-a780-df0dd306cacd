BKPAAS_APP_ID=bkflow
BKPAAS_APP_SECRET=bkflow_secret
BKPAAS_MAJOR_VERSION=3
BK_PAAS_HOST=bk_paas_host
BK_PAAS2_URL=bk_paas2_url
BKPAAS_LOGIN_URL=login_url
BKPAAS_REMOTE_STATIC_URL=static_url
BKPAAS_APIGW_OAUTH_API_URL=apigw_url
DB_NAME=bkflow
DJANGO_SETTINGS_MODULE=settings
BKFLOW_MODULE_TYPE=interface
BK_APIGW_NETLOC_PATTERN=^www\.test\.com/(?P<api_name>[\w-]+)
BK_API_URL_TMPL=http://{api_name}.example.com

BK_APIGW_REQUIRE_EXEMPT=1
SKIP_APIGW_CHECK=1

APP_INTERNAL_TOKEN=123456
ENABLE_BK_PLUGIN_AUTHORIZATION=1

