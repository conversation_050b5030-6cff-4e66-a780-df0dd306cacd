# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-04 16:06+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: bkflow/admin/models.py:40
msgid "任务模块"
msgstr "Module"

#: bkflow/admin/models.py:43
msgid "仅隔离计算"
msgstr "Only isolation calculation"

#: bkflow/admin/models.py:44
msgid "全部隔离"
msgstr "All isolation"

#: bkflow/admin/models.py:47 bkflow/apigw/serializers/template.py:82
#: bkflow/permission/models.py:71 bkflow/space/models.py:58
#: bkflow/space/models.py:151 bkflow/space/models.py:200
#: bkflow/space/serializers.py:60 bkflow/space/serializers.py:64
#: bkflow/template/models.py:43 bkflow/template/serializers/template.py:177
#: bkflow/template/serializers/template.py:209
#: bkflow/template/serializers/template.py:217
#: bkflow/template/serializers/template.py:223
msgid "空间ID"
msgstr "Space ID"

#: bkflow/admin/models.py:48
msgid "模块code"
msgstr "Module code"

#: bkflow/admin/models.py:49
msgid "模块提供的地址"
msgstr "The address provided by the module"

#: bkflow/admin/models.py:50
msgid "模块的token"
msgstr "The token of the module"

#: bkflow/admin/models.py:51
msgid "模块类型"
msgstr "Module type"

#: bkflow/admin/models.py:52
msgid "隔离类型"
msgstr "Isolation"

#: bkflow/admin/models.py:55 bkflow/admin/models.py:56
msgid "模块信息表"
msgstr "Module information table"

#: bkflow/apigw/exceptions.py:27
msgid "创建Token失败"
msgstr "Creating token failed"

#: bkflow/apigw/exceptions.py:33
msgid "模板更新失败"
msgstr "Template update failed"

#: bkflow/apigw/exceptions.py:39
msgid "分页参数校验失败"
msgstr "Pagling parameter check failure"

#: bkflow/apigw/serializers/credential.py:25
msgid "凭证名称"
msgstr "Voucher name"

#: bkflow/apigw/serializers/credential.py:26 bkflow/space/models.py:202
msgid "凭证描述"
msgstr "Voucher description"

#: bkflow/apigw/serializers/credential.py:27 bkflow/space/models.py:203
msgid "凭证类型"
msgstr "Document Type"

#: bkflow/apigw/serializers/credential.py:28 bkflow/space/models.py:204
msgid "凭证内容"
msgstr "Voucher content"

#: bkflow/apigw/serializers/space.py:34 bkflow/space/models.py:60
msgid "空间名称"
msgstr "Space name"

#: bkflow/apigw/serializers/space.py:35 bkflow/space/models.py:62
msgid "空间描述"
msgstr "Space description"

#: bkflow/apigw/serializers/space.py:36 bkflow/space/models.py:63
msgid "平台提供服务的地址"
msgstr "The platform provides the address of the service"

#: bkflow/apigw/serializers/space.py:37
msgid "app id"
msgstr "app ID"

#: bkflow/apigw/serializers/space.py:39
#: bkflow/apigw/serializers/space_config.py:28
msgid "配置信息"
msgstr "Configuration information"

#: bkflow/apigw/serializers/space.py:49
#: bkflow/apigw/serializers/space_config.py:33
#, python-brace-format
msgid "配置信息中存在不支持的配置项, 支持的配置有: {support_choices}"
msgstr ""
"There are unwilling configuration items in the configuration information. "
"The supporting configurations are: {support_choices}"

#: bkflow/apigw/serializers/task.py:31 bkflow/apigw/serializers/task.py:59
#: bkflow/template/models.py:42
msgid "模版ID"
msgstr "Template ID"

#: bkflow/apigw/serializers/task.py:32 bkflow/apigw/serializers/task.py:47
#: bkflow/apigw/serializers/task.py:81 bkflow/apigw/serializers/task.py:110
msgid "任务名"
msgstr "Task name"

#: bkflow/apigw/serializers/task.py:33 bkflow/apigw/serializers/task.py:48
#: bkflow/apigw/serializers/task.py:82 bkflow/apigw/serializers/task.py:109
msgid "创建者"
msgstr "creator"

#: bkflow/apigw/serializers/task.py:34 bkflow/apigw/serializers/task.py:50
#: bkflow/apigw/serializers/task.py:85
msgid "任务描述"
msgstr "mission details"

#: bkflow/apigw/serializers/task.py:35 bkflow/apigw/serializers/task.py:51
#: bkflow/apigw/serializers/task.py:86
msgid "任务启动参数"
msgstr "Task startup parameter"

#: bkflow/apigw/serializers/task.py:39
msgid "要 Mock 执行的节点 ID 列表"
msgstr "Node ID list of mock execution"

#: bkflow/apigw/serializers/task.py:40
msgid "节点 Mock 输出, 形如{\"node_id\": {\"output1\": \"output_value1\"}}"
msgstr ""
"Node mock output, shaped like {\"node_id\": {\"Output1\": \"Output_value1\"}}"

#: bkflow/apigw/serializers/task.py:42
msgid ""
"节点 Mock 数据，当 outputs 为空时会提取对应 mock_data_ids 设置 outputs，否则"
"仅记录作用"
msgstr ""

#: bkflow/apigw/serializers/task.py:49
msgid "Mock 数据"
msgstr "MOCK data"

#: bkflow/apigw/serializers/task.py:55 bkflow/apigw/serializers/task.py:87
#: bkflow/apigw/serializers/task.py:92 bkflow/apigw/serializers/template.py:48
msgid "任务树"
msgstr "Mission tree"

#: bkflow/apigw/serializers/task.py:83
msgid "任务范围类型"
msgstr "Task scope type"

#: bkflow/apigw/serializers/task.py:84
msgid "任务范围值"
msgstr "Task scope value"

#: bkflow/apigw/serializers/task.py:88 bkflow/apigw/serializers/template.py:41
#: bkflow/apigw/serializers/template.py:94
msgid "通知配置"
msgstr "Notification configuration"

#: bkflow/apigw/serializers/task.py:103 bkflow/apigw/serializers/template.py:43
#: bkflow/apigw/serializers/template.py:96
#: bkflow/apigw/serializers/template.py:114 bkflow/template/models.py:48
msgid "流程范围类型"
msgstr "Process type type"

#: bkflow/apigw/serializers/task.py:104 bkflow/apigw/serializers/template.py:44
#: bkflow/apigw/serializers/template.py:97
#: bkflow/apigw/serializers/template.py:115
msgid "流程范围值"
msgstr "Range value"

#: bkflow/apigw/serializers/task.py:105
msgid "偏移量"
msgstr "Offset"

#: bkflow/apigw/serializers/task.py:106
msgid "返回数量"
msgstr "Return quantity"

#: bkflow/apigw/serializers/task.py:107
msgid "创建时间开始"
msgstr "Create time start"

#: bkflow/apigw/serializers/task.py:108
msgid "创建时间结束"
msgstr "End of the creation time"

#: bkflow/apigw/serializers/task.py:118 bkflow/apigw/serializers/task.py:122
#: bkflow/contrib/operation_record/models.py:26
msgid "操作人"
msgstr "Operator"

#: bkflow/apigw/serializers/task.py:126
msgid "循环次数"
msgstr "Cycles"

#: bkflow/apigw/serializers/task.py:127
msgid "组件code"
msgstr "Component Code"

#: bkflow/apigw/serializers/template.py:38
#: bkflow/apigw/serializers/template.py:112 bkflow/utils/models.py:33
msgid "创建人"
msgstr "founder"

#: bkflow/apigw/serializers/template.py:39
msgid "来源的模板id"
msgstr "Source template ID"

#: bkflow/apigw/serializers/template.py:40
#: bkflow/apigw/serializers/template.py:93 bkflow/template/models.py:45
msgid "模版名称"
msgstr "Template name"

#: bkflow/apigw/serializers/template.py:42
#: bkflow/apigw/serializers/template.py:95
#: bkflow/pipeline_plugins/components/collections/pause/legacy.py:47
#: bkflow/pipeline_plugins/components/collections/pause/legacy.py:50
#: bkflow/template/models.py:46
msgid "描述"
msgstr "describe"

#: bkflow/apigw/serializers/template.py:45
#: bkflow/apigw/serializers/template.py:98 bkflow/template/models.py:50
msgid "来源"
msgstr "source"

#: bkflow/apigw/serializers/template.py:46
#: bkflow/apigw/serializers/template.py:99 bkflow/template/models.py:51
msgid "版本号"
msgstr "version number"

#: bkflow/apigw/serializers/template.py:47
#: bkflow/apigw/serializers/template.py:100
msgid "额外扩展信息"
msgstr "Additional extension information"

#: bkflow/apigw/serializers/template.py:57
#, fuzzy, python-brace-format
#| msgid "复制的源模板不存在, 请检查: {}"
msgid "复制的源模板不存在, 请检查: {source_template_id}"
msgstr ""
"Copy the source template does not exist, please check: {source_template_id}"

#: bkflow/apigw/serializers/template.py:61
#, fuzzy, python-brace-format
#| msgid "只能复制同一个空间下的模板, space_id={}"
msgid "只能复制同一个空间下的模板, space_id={space_id}"
msgstr ""
"You can only copy the template under the same space, space_id = {space_id}"

#: bkflow/apigw/serializers/template.py:71
#: bkflow/template/serializers/template.py:95
msgid "参数校验失败，pipeline校验不通过, err={}"
msgstr "Parameter check failed, pipeline did not pass, err = {}"

#: bkflow/apigw/serializers/template.py:75
msgid "网关用户和creator都为空，请检查"
msgstr "Both the gateway users and the creator are empty, please check"

#: bkflow/apigw/serializers/template.py:81 bkflow/template/models.py:118
#: bkflow/template/serializers/template.py:210
#: bkflow/template/serializers/template.py:218
#: bkflow/template/serializers/template.py:224
msgid "模板ID"
msgstr "Template ID"

#: bkflow/apigw/serializers/template.py:86
#, fuzzy, python-brace-format
#| msgid "校验失败，space_id={}对应的空间不存在"
msgid "校验失败，space_id={space_id}对应的空间不存在"
msgstr ""
"The verification failure, space_id = {space_id} corresponding space does not "
"exist"

#: bkflow/apigw/serializers/template.py:92
#: bkflow/apigw/serializers/template.py:113
msgid "更新人"
msgstr "updater"

#: bkflow/apigw/serializers/template.py:105
msgid "网关用户和operator都为空，请检查"
msgstr "The gateway users and operators are empty, please check"

#: bkflow/apigw/serializers/template.py:111
msgid "模板名称"
msgstr "Template name"

#: bkflow/apigw/serializers/template.py:116
msgid "开始时间小于等于"
msgstr "Start time is less than equal to"

#: bkflow/apigw/serializers/template.py:117
msgid "开始时间大于等于"
msgstr "Start time is greater than equal to"

#: bkflow/apigw/serializers/template.py:118
msgid "排序字段"
msgstr "Sort field"

#: bkflow/apigw/serializers/template.py:122
msgid "是否包含 mock 数据"
msgstr "Whether it contains mock data"

#: bkflow/apigw/serializers/token.py:61
msgid "token申请失败，不支持的资源类型"
msgstr ""
"Token application failed, the type of resource type that does not support"

#: bkflow/apigw/serializers/token.py:64
msgid "token申请失败，对应的资源不存在"
msgstr "Token failed, the corresponding resources did not exist"

#: bkflow/apigw/serializers/token.py:72 bkflow/apigw/serializers/token.py:82
#: bkflow/permission/models.py:73
msgid "资源类型"
msgstr "Resource Type"

#: bkflow/apigw/serializers/token.py:73 bkflow/apigw/serializers/token.py:83
#: bkflow/permission/models.py:74
msgid "资源ID"
msgstr "Resource ID"

#: bkflow/apigw/serializers/token.py:74 bkflow/apigw/serializers/token.py:84
#: bkflow/permission/models.py:76
msgid "权限类型"
msgstr "Permissions"

#: bkflow/apigw/serializers/token.py:80
msgid "token"
msgstr "token"

#: bkflow/apigw/serializers/token.py:81
msgid "user"
msgstr "user"

#: bkflow/apigw/views/apply_token.py:68
msgid "用户名不能为空"
msgstr "Username can not be empty"

#: bkflow/apigw/views/create_mock_task.py:50
#: bkflow/apigw/views/create_task.py:50 bkflow/template/views/template.py:134
#, fuzzy, python-brace-format
#| msgid "模版不存在，space_id={}, template_id={}"
msgid "模版不存在，space_id={space_id}, template_id={template_id}"
msgstr ""
"The template does not exist, space_id = {space_id}, template_id = "
"{template_id}"

#: bkflow/apigw/views/update_template.py:65
#, fuzzy, python-brace-format
#| msgid "请检查参数，params:{}"
msgid "请检查参数，params:{validated_data_dict}"
msgstr "Please check the parameters, params: {value_data_dict}"

#: bkflow/constants.py:48 bkflow/constants.py:78
msgid "创建"
msgstr "create"

#: bkflow/constants.py:49 bkflow/constants.py:79
msgid "删除"
msgstr "delete"

#: bkflow/constants.py:50 bkflow/constants.py:80
msgid "修改"
msgstr "Revise"

#: bkflow/constants.py:51
msgid "执行"
msgstr "implement"

#: bkflow/constants.py:52
#: bkflow/pipeline_plugins/components/collections/pause/legacy.py:69
msgid "暂停"
msgstr "pause"

#: bkflow/constants.py:53
msgid "继续"
msgstr "continue"

#: bkflow/constants.py:54
msgid "撤消"
msgstr "Dismiss"

#: bkflow/constants.py:57
msgid "回调"
msgstr "Call back"

#: bkflow/constants.py:58
msgid "重试"
msgstr "Retry"

#: bkflow/constants.py:59
msgid "跳过"
msgstr "jump over"

#: bkflow/constants.py:60
msgid "跳过失败网关"
msgstr "Skip the failure gateway"

#: bkflow/constants.py:61
msgid "跳过并行条件网关"
msgstr "Jump over parallel condition gateway"

#: bkflow/constants.py:62
msgid "暂停节点"
msgstr "Pause node"

#: bkflow/constants.py:63
msgid "继续节点"
msgstr "Continue node"

#: bkflow/constants.py:64
msgid "强制失败"
msgstr "Compulsory failure"

#: bkflow/constants.py:66
msgid "任务操作"
msgstr "Mission operation"

#: bkflow/constants.py:67
msgid "节点操作"
msgstr "Node operation"

#: bkflow/constants.py:73 bkflow/constants.py:86
msgid "app 页面"
msgstr "app page"

#: bkflow/constants.py:74 bkflow/constants.py:87
msgid "api 接口"
msgstr "API interface"

#: bkflow/constants.py:93
msgid "任务实例"
msgstr "Mission instance"

#: bkflow/constants.py:94
msgid "任务节点"
msgstr "Mission node"

#: bkflow/constants.py:95
msgid "模版实例"
msgstr "Template instance"

#: bkflow/contrib/operation_record/models.py:25
msgid "ID"
msgstr "ID"

#: bkflow/contrib/operation_record/models.py:27 bkflow/task/models.py:359
#: bkflow/template/models.py:134
msgid "操作类型"
msgstr "Type"

#: bkflow/contrib/operation_record/models.py:28 bkflow/task/models.py:362
#: bkflow/template/models.py:137
msgid "操作来源"
msgstr "Source"

#: bkflow/contrib/operation_record/models.py:29
msgid "记录对象实例ID"
msgstr "Record object instance ID"

#: bkflow/contrib/operation_record/models.py:30
msgid "操作时间"
msgstr "Operation time"

#: bkflow/contrib/operation_record/models.py:31
msgid "额外信息"
msgstr "extra information"

#: bkflow/interface/task/view.py:145
#, fuzzy, python-brace-format
#| msgid "当前token已过期或不存在，token={}, user={}"
msgid "当前token已过期或不存在，token={token}, user={username}"
msgstr ""
"The current token has expired or does not exist, token = {token}, user = "
"{username}"

#: bkflow/interface/views.py:118
#, fuzzy, python-brace-format
#| msgid ""
#| "节点回调失败: 无效的请求, 请重试. 如持续失败可联系管理员处理. {traceback."
#| "format_exc()} | api callback"
msgid ""
"节点回调失败: 无效的请求, 请重试. 如持续失败可联系管理员处理. {msg} | api "
"callback"
msgstr ""
"Node recovery failure: invalid request, please repeat. If you continue to "
"fail, contact the administrator for processing. {Msg} | API callback"

#: bkflow/interface/views.py:130
#, fuzzy, python-brace-format
#| msgid ""
#| "节点回调失败: 请求失败task模块失败. {traceback.format_exc()} | api "
#| "callback"
msgid "节点回调失败: 请求失败task模块失败. {msg} | api callback"
msgstr ""
"Node recovery failure: the request failure TASK module fails. {Msg} | API "
"callback"

#: bkflow/permission/exceptions.py:27
msgid "Token不存在"
msgstr "Token does not exist"

#: bkflow/permission/exceptions.py:33
msgid "Token续期失败"
msgstr "Token renewal failure"

#: bkflow/permission/models.py:60
msgid "任务"
msgstr "Task"

#: bkflow/permission/models.py:61
msgid "流程"
msgstr "process"

#: bkflow/permission/models.py:65
msgid "查看"
msgstr "Check"

#: bkflow/permission/models.py:66
msgid "编辑"
msgstr "edit"

#: bkflow/permission/models.py:67
msgid "操作"
msgstr "operate"

#: bkflow/permission/models.py:68
msgid "调试"
msgstr "debug"

#: bkflow/permission/models.py:70
msgid "Token值"
msgstr "Token value"

#: bkflow/permission/models.py:72
msgid "用户名"
msgstr "username"

#: bkflow/permission/models.py:78
msgid "过期时间"
msgstr "Expiration"

#: bkflow/permission/models.py:81 bkflow/permission/models.py:82
msgid "token 表"
msgstr "token table"

#: bkflow/permission/views.py:45
msgid "Token 续期失败，当前续期的用户与正在登录的用户不一致"
msgstr ""
"Token renewal failure, the current renewal users are inconsistent with users "
"who are logging in"

#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:29
#: bkflow/pipeline_plugins/components/collections/debug_plugin/v1_0_0.py:34
#: bkflow/pipeline_plugins/components/collections/display/v1_0.py:28
#: bkflow/pipeline_plugins/components/collections/dmn_plugin/v1_0_0.py:30
#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:44
#: bkflow/pipeline_plugins/components/collections/pause/legacy.py:29
#: bkflow/pipeline_plugins/components/collections/sleep_time/legacy.py:35
#: bkflow/pipeline_plugins/components/collections/uniform_api/v1_0_0.py:32
msgid "蓝鲸服务(BK)"
msgstr "BlueKing Service (BK)"

#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:43
msgid "审核人"
msgstr "Reviewer"

#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:46
msgid "审核人,多个用英文逗号`,`分隔"
msgstr "The reviewer, multiple in English comma `,` separate"

#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:49
#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:52
msgid "审核标题"
msgstr "Review title"

#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:55
msgid "审核内容"
msgstr "Review content"

#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:58
msgid "通知的标题"
msgstr "The title of the notification"

#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:65
msgid "单据sn"
msgstr "According to SN"

#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:68
#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:71
msgid "审核结果"
msgstr "Audit results"

#: bkflow/pipeline_plugins/components/collections/approve/v1_0.py:122
msgid "审批"
msgstr "Approve"

#: bkflow/pipeline_plugins/components/collections/display/v1_0.py:35
#: bkflow/pipeline_plugins/components/collections/display/v1_0.py:38
msgid "展示内容"
msgstr "Display content"

#: bkflow/pipeline_plugins/components/collections/display/v1_0.py:50
msgid "消息展示"
msgstr "Message display"

#: bkflow/pipeline_plugins/components/collections/display/v1_0.py:55
msgid "本插件为仅用于消息展示的空节点"
msgstr "This plugin is an empty node used for message display"

#: bkflow/pipeline_plugins/components/collections/dmn_plugin/v1_0_0.py:39
#: bkflow/pipeline_plugins/components/collections/dmn_plugin/v1_0_0.py:42
msgid "决策表"
msgstr "Decision table"

#: bkflow/pipeline_plugins/components/collections/dmn_plugin/v1_0_0.py:45
#: bkflow/pipeline_plugins/components/collections/dmn_plugin/v1_0_0.py:48
msgid "决策表 facts"
msgstr "Decision Facts"

#: bkflow/pipeline_plugins/components/collections/dmn_plugin/v1_0_0.py:93
msgid "决策插件"
msgstr "Decision plugin"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:54
#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:57
msgid "HTTP 请求方法"
msgstr "HTTP request method"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:60
#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:63
msgid "HTTP 请求目标地址"
msgstr "Http request target address"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:66
msgid "HTTP 请求 header"
msgstr "Http request header"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:70
msgid "HTTP 请求头部列表"
msgstr "Http request head list"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:72
msgid "单个头部信息"
msgstr "Single head information"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:74
msgid "请求头名称"
msgstr "The first name of the request"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:75
msgid "请求头值"
msgstr "Request head value"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:81
#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:84
msgid "HTTP 请求 body"
msgstr "Http request body"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:87
#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:90
msgid "HTTP 请求超时时间"
msgstr "Http request timeout time"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:93
msgid "HTTP 请求成功条件"
msgstr "HTTP request success condition"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:98
msgid ""
"根据返回的 JSON 的数据来控制节点的成功或失败, 使用 resp 引用返回的 JSON 对"
"象，例 resp.result==True"
msgstr ""
"Control the success or failure of the node based on the data returned by the "
"returned JSON, use resp to reference the returned JSON object, the example "
"resp.result == TRUE"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:108
#: bkflow/pipeline_plugins/components/collections/uniform_api/v1_0_0.py:42
#: bkflow/pipeline_plugins/components/collections/uniform_api/v2_0_0.py:71
msgid "响应内容"
msgstr "Response content"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:111
#: bkflow/pipeline_plugins/components/collections/uniform_api/v1_0_0.py:45
#: bkflow/pipeline_plugins/components/collections/uniform_api/v2_0_0.py:74
msgid "HTTP 请求响应内容，内部结构不固定"
msgstr "HTTP request response content, the internal structure is not fixed"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:114
#: bkflow/pipeline_plugins/components/collections/uniform_api/v1_0_0.py:48
#: bkflow/pipeline_plugins/components/collections/uniform_api/v2_0_0.py:77
msgid "状态码"
msgstr "status code"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:117
#: bkflow/pipeline_plugins/components/collections/uniform_api/v1_0_0.py:51
#: bkflow/pipeline_plugins/components/collections/uniform_api/v2_0_0.py:80
msgid "HTTP 请求响应状态码"
msgstr "HTTP request response status code"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:151
#, fuzzy, python-brace-format
#| msgid "请求异常，详细信息: {}"
msgid "请求异常，详细信息: {msg}"
msgstr "Request abnormal, detailed information: {msg}"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:166
msgid "请求响应数据格式非 JSON"
msgstr "Request response data format non-JSON"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:172
#, fuzzy, python-brace-format
#| msgid "请求失败，状态码: {}，响应: {}"
msgid "请求失败，状态码: {status_code}，响应: {resp}"
msgstr "The request failed, the status code: {status_code}, response: {resp}"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:181
msgid "请求成功判定失败"
msgstr "Request successful judgment failed"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:184
#, fuzzy, python-brace-format
#| msgid "请求成功条件判定出错: {}"
msgid "请求成功条件判定出错: {msg}"
msgstr "The request successful condition is determined to make an error: {msg}"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:200
msgid "HTTP 请求"
msgstr "HTTP request"

#: bkflow/pipeline_plugins/components/collections/http/v1_0.py:202
msgid ""
"提示: 1.请求URL需要在当前网络下可以访问，否则会超时失败 2.响应状态码在"
"200-300(不包括300)之间，并且响应内容是 JSON 格式才会执行成功"
msgstr ""
"Tip: 1. Request URL needs to be accessed under the current network, "
"otherwise it will fail time. 2. The response status code is between 200-300 "
"(excluding 300), and the response content is that the execution of the JSON "
"format will be successfully implemented before the execution is successful."

#: bkflow/pipeline_plugins/components/collections/pause/legacy.py:57
msgid "API回调数据"
msgstr "API callback data"

#: bkflow/pipeline_plugins/components/collections/pause/legacy.py:61
msgid "通过node_callback API接口回调并传入数据,支持dict数据"
msgstr ""
"Back up and pass through the Node_callback API interface, support DICT data"

#: bkflow/pipeline_plugins/components/collections/pause/legacy.py:74
msgid ""
"该节点可以通过node_callback API接口进行回调并传入数据，callback_data参数为"
"dict类型，回调数据会作为该节点的输出数据"
msgstr ""
"This node can be adjusted and transmitted through the Node_callback API "
"interface. The callback_data parameter is the DICT type."

#: bkflow/pipeline_plugins/components/collections/remote_plugin/v1_0_0.py:67
#: bkflow/pipeline_plugins/components/collections/remote_plugin/v1_0_0.py:123
#, python-brace-format
msgid "第三方插件client初始化失败, 错误内容: {e}"
msgstr "Third -party plug -in client initialization failed, error content: {e}"

#: bkflow/pipeline_plugins/components/collections/remote_plugin/v1_0_0.py:74
#, fuzzy, python-brace-format
#| msgid "获取第三方插件详情失败, 错误内容: {detail_result['message']}"
msgid "获取第三方插件详情失败, 错误内容: {message}"
msgstr ""
"Failure for the details of obtaining third -party plug -in, error content: "
"{message}"

#: bkflow/pipeline_plugins/components/collections/remote_plugin/v1_0_0.py:92
#, fuzzy, python-brace-format
#| msgid ""
#| "调用第三方插件invoke接口错误, 错误内容: {result_data['message']}, "
#| "trace_id: {result_data.get('trace_id')}"
msgid "调用第三方插件invoke接口错误, 错误内容: {message}, trace_id: {trace_id}"
msgstr ""
"Call the third -party plug -in Invoke interface error, error content: "
"{message}, trace_id: {trace_id}"

#: bkflow/pipeline_plugins/components/collections/remote_plugin/v1_0_0.py:143
msgid "请通过第三方节点日志查看任务失败原因"
msgstr ""
"Please see the reason for the failure of the task through a third -party "
"node log"

#: bkflow/pipeline_plugins/components/collections/sleep_time/legacy.py:59
msgid "定时时间"
msgstr "Timing"

#: bkflow/pipeline_plugins/components/collections/sleep_time/legacy.py:62
#, python-format
msgid "定时时间，格式为秒(s) 或 (%%Y-%%m-%%d %%H:%%M:%%S)"
msgstr "Time time, format is second (s) or (%% y-%% m-%% D %% H: %% S)"

#: bkflow/pipeline_plugins/components/collections/sleep_time/legacy.py:65
msgid "是否强制晚于当前时间"
msgstr "Whether to force later than the current time"

#: bkflow/pipeline_plugins/components/collections/sleep_time/legacy.py:69
msgid ""
"用户输入日期格式时是否强制要求时间晚于当前时间，只对日期格式定时输入有效"
msgstr ""
"Whether the user enters the date format for compulsory time is later than "
"the current time, only the time input of the date format is effective"

#: bkflow/pipeline_plugins/components/collections/sleep_time/legacy.py:92
msgid "定时时间需晚于当前时间"
msgstr "Time time needs to be later than the current time"

#: bkflow/pipeline_plugins/components/collections/sleep_time/legacy.py:99
#, python-format
msgid "输入参数%s不符合【秒(s) 或 时间(%%Y-%%m-%%d %%H:%%M:%%S)】格式"
msgstr ""
"Input parameters%s does not meet [second (s) or time (%% y-%% m-%% D %% h: "
"%% s)] format"

#: bkflow/pipeline_plugins/components/collections/sleep_time/legacy.py:134
msgid "定时"
msgstr "timing"

#: bkflow/pipeline_plugins/components/collections/uniform_api/v1_0_0.py:143
#: bkflow/pipeline_plugins/components/collections/uniform_api/v2_0_0.py:363
msgid "统一API调用"
msgstr "Unified API call"

#: bkflow/pipeline_plugins/components/collections/uniform_api/v1_0_0.py:146
#: bkflow/pipeline_plugins/components/collections/uniform_api/v2_0_0.py:366
msgid "用于调用符合接口协议的统一API"
msgstr "Used to call the unified API that meets the interface protocol"

#: bkflow/pipeline_plugins/variables/collections/bk_user_selector.py:36
msgid "人员选择器"
msgstr "Personnel selection device"

#: bkflow/pipeline_plugins/variables/collections/datatable.py:51
msgid "表格"
msgstr "sheet"

#: bkflow/pipeline_plugins/variables/collections/datatable.py:56
msgid "表格变量"
msgstr "Form variable"

#: bkflow/pipeline_plugins/variables/collections/datatable.py:58
#, python-brace-format
msgid ""
"引用表格变量某一列某一行的属性，如 ${KEY.columnA[0]} -> \"test1\"\n"
"引用表格变量某一列的全部属性，多行用换行符 `\\n` 分隔，如 ${KEY."
"flat__columnA} -> \"test1\n"
"test2\""
msgstr ""
"Quote Form variable attributes of a certain line, such as ${KEY.columnA[0]} -"
"> \"test1\"\n"
"The reference table variables all the attributes of a certain column, multi -"
"line use the change character `\\n` separate, such as ${KEY.flat__columnA} ->"
"\"test1\n"
"test2\""

#: bkflow/pipeline_plugins/variables/collections/datetime.py:36
msgid "日期时间"
msgstr "Date"

#: bkflow/pipeline_plugins/variables/collections/datetime.py:40
msgid "日期时间变量"
msgstr "Date -time variable"

#: bkflow/pipeline_plugins/variables/collections/datetime.py:41
msgid "输出格式: 2000-04-19 14:45:16"
msgstr "Output format: 2000-04-19 14:45:16"

#: bkflow/pipeline_plugins/variables/collections/datetime_range.py:36
msgid "日期时间范围"
msgstr "Date"

#: bkflow/pipeline_plugins/variables/collections/datetime_range.py:40
msgid "日期时间范围变量"
msgstr "Date -time range variable"

#: bkflow/pipeline_plugins/variables/collections/datetime_range.py:41
msgid "输出格式: [\"2023-01-1 00:00:00\", \"2023-01-01 23:59:59\"]"
msgstr "Output format: [\"2023-01-1 00:00:00\", \"2023-01-01 23:59:59\"]"

#: bkflow/pipeline_plugins/variables/collections/input.py:36
msgid "输入框"
msgstr "Input box"

#: bkflow/pipeline_plugins/variables/collections/input.py:40
msgid "输入框变量"
msgstr "Input box variable"

#: bkflow/pipeline_plugins/variables/collections/int.py:36
msgid "整数"
msgstr "Integer"

#: bkflow/pipeline_plugins/variables/collections/int.py:40
msgid "整数变量"
msgstr "Integer variable"

#: bkflow/pipeline_plugins/variables/collections/json_variable.py:30
msgid "JSON 变量"
msgstr "Json variable"

#: bkflow/pipeline_plugins/variables/collections/json_variable.py:34
msgid "内部结构不固定"
msgstr "Internal structure is not fixed"

#: bkflow/pipeline_plugins/variables/collections/select.py:36
msgid "下拉框"
msgstr "Drop -down box"

#: bkflow/pipeline_plugins/variables/collections/select.py:41
msgid "下拉框变量"
msgstr "Drop -down box variable"

#: bkflow/pipeline_plugins/variables/collections/select.py:43
msgid ""
"单选模式下输出选中的 value，多选模式下输出选中 value 以 ',' 拼接的字符串\n"
"该变量默认不支持输入任意值，仅在子流程节点配置填参时支持输入任意值"
msgstr ""
"Output the selected value in a single selection mode, and output the "
"selected Value string string stringed stringed string in multiple selection "
"mode\n"
"This variable does not support the input of an arbitrary value by default, "
"and supports the input arbitrary value when filling in the sub -process node "
"configuration"

#: bkflow/pipeline_plugins/variables/collections/textarea.py:36
msgid "文本框"
msgstr "Text box"

#: bkflow/pipeline_plugins/variables/collections/textarea.py:40
msgid "文本框变量"
msgstr "Text box variable"

#: bkflow/pipeline_web/core/abstract.py:27
#: bkflow/template/serializers/template.py:219
#: bkflow/template/serializers/template.py:229
msgid "节点ID"
msgstr "Node ID"

#: bkflow/pipeline_web/core/abstract.py:28
msgid "节点类型"
msgstr "Node type"

#: bkflow/pipeline_web/core/abstract.py:29 bkflow/template/models.py:223
#: bkflow/template/models.py:240 bkflow/utils/models.py:34
#: bkflow/utils/models.py:68
msgid "创建时间"
msgstr "Creation time"

#: bkflow/pipeline_web/core/abstract.py:30
msgid "修改时间"
msgstr "Change the time"

#: bkflow/pipeline_web/core/models.py:99
msgid "所属模板ID"
msgstr "Template ID"

#: bkflow/pipeline_web/core/models.py:100
msgid "所属模板版本"
msgstr "Template version"

#: bkflow/pipeline_web/core/models.py:105
#: bkflow/pipeline_web/core/models.py:106
msgid "流程模板节点 NodeInTemplate"
msgstr "Process template node nodeIntemplate"

#: bkflow/pipeline_web/core/models.py:112
msgid "流程模板节点"
msgstr "Process template node"

#: bkflow/pipeline_web/core/models.py:142
msgid "所属实例ID"
msgstr "A belonging to instance ID"

#: bkflow/pipeline_web/core/models.py:147
#: bkflow/pipeline_web/core/models.py:148
msgid "流程实例节点 NodeInInstance"
msgstr "Process instance node nodeininstance"

#: bkflow/pipeline_web/core/models.py:153
msgid "流程实例节点"
msgstr "Process instance node"

#: bkflow/pipeline_web/label/models.py:30
msgid "标签分组编码"
msgstr "Label packet coding"

#: bkflow/pipeline_web/label/models.py:31
msgid "标签分组名称"
msgstr "Tag group name"

#: bkflow/pipeline_web/label/models.py:34
#: bkflow/pipeline_web/label/models.py:35
msgid "标签分组 LabelGroup"
msgstr "Label group labelgroup"

#: bkflow/pipeline_web/label/models.py:46
msgid "标签编码"
msgstr "Label"

#: bkflow/pipeline_web/label/models.py:47
msgid "标签名称"
msgstr "Tag name"

#: bkflow/pipeline_web/label/models.py:50
#: bkflow/pipeline_web/label/models.py:51
msgid "标签 Label"
msgstr "Tag Label"

#: bkflow/pipeline_web/label/models.py:77
#: bkflow/pipeline_web/label/models.py:90
msgid "节点标签"
msgstr "Node label"

#: bkflow/pipeline_web/label/models.py:82
#: bkflow/pipeline_web/label/models.py:83
msgid "流程模板节点标签 NodeInTemplateAttrLabel"
msgstr "Process template node label nodeintemplatetrlabel"

#: bkflow/pipeline_web/label/models.py:95
#: bkflow/pipeline_web/label/models.py:96
msgid "流程实例节点标签 NodeInInstanceAttrLabel"
msgstr "Process instance node label nodeininstancettrlabel"

#: bkflow/pipeline_web/plugin_management/models.py:82
msgid "插件编码"
msgstr "Plug -in encoding"

#: bkflow/pipeline_web/plugin_management/models.py:83
msgid "插件版本"
msgstr "Plug -in version"

#: bkflow/pipeline_web/plugin_management/models.py:84
msgid "插件类型"
msgstr "Plug -in"

#: bkflow/pipeline_web/plugin_management/models.py:85
msgid "生命周期"
msgstr "life cycle"

#: bkflow/space/configs.py:119
msgid "Token过期时间"
msgstr "Token Expired time"

#: bkflow/space/configs.py:152
msgid "是否开启Token自动续期"
msgstr "Whether to open the token automatic renewal"

#: bkflow/space/configs.py:168
msgid "回调配置"
msgstr "Callback configuration"

#: bkflow/space/configs.py:206
msgid "是否开启统一API"
msgstr "Whether to open a unified API"

#: bkflow/space/configs.py:243
msgid "空间管理员"
msgstr "Space administrator"

#: bkflow/space/configs.py:257
msgid "画布模式"
msgstr "Canvas mode"

#: bkflow/space/configs.py:272
msgid "网关表达式"
msgstr "Gateway expression"

#: bkflow/space/configs.py:288
msgid "API_GATEWAY使用的凭证名称"
msgstr "The voucher name used by API_Gateway"

#: bkflow/space/configs.py:293
msgid "空间插件配置"
msgstr "Space plug -in configuration"

#: bkflow/space/credential.py:67
msgid "type={}"
msgstr "Type = {}"

#: bkflow/space/exceptions.py:27
msgid "该空间配置项没有配置默认值"
msgstr "The space configuration item does not configure the default value"

#: bkflow/space/exceptions.py:33
msgid "不支持该凭证操作"
msgstr "Do not support the proof operation"

#: bkflow/space/exceptions.py:38
msgid "凭证不存在"
msgstr "The voucher does not exist"

#: bkflow/space/exceptions.py:43
msgid "不支持的凭证类型"
msgstr "Unsuitable voucher type"

#: bkflow/space/exceptions.py:48
msgid "空间不存在"
msgstr "Space does not exist"

#: bkflow/space/models.py:54
msgid "API"
msgstr "API"

#: bkflow/space/models.py:55
msgid "WEB"
msgstr "Web"

#: bkflow/space/models.py:61
msgid "应用ID"
msgstr "App ID"

#: bkflow/space/models.py:65
msgid "空间创建的方式"
msgstr "How to create space"

#: bkflow/space/models.py:85
msgid "空间信息"
msgstr "Space information"

#: bkflow/space/models.py:86
msgid "空间信息表"
msgstr "Space information table"

#: bkflow/space/models.py:148
msgid "文本"
msgstr "text"

#: bkflow/space/models.py:153
msgid "配置类型"
msgstr "Configuration"

#: bkflow/space/models.py:155
msgid "配置项"
msgstr "Configuration item"

#: bkflow/space/models.py:156
msgid "配置值"
msgstr "Configuration"

#: bkflow/space/models.py:157
msgid "配置值(JSON)"
msgstr "Configuration value (json)"

#: bkflow/space/models.py:162 bkflow/space/serializers.py:65
msgid "空间配置"
msgstr "Space configuration"

#: bkflow/space/models.py:163
msgid "空间配置表"
msgstr "Space configuration table"

#: bkflow/space/models.py:188
msgid "不存在该配置项"
msgstr "There is no existence of this configuration item"

#: bkflow/space/models.py:198
msgid "蓝鲸应用凭证"
msgstr "BlueKing Application Voucher"

#: bkflow/space/models.py:201
msgid "凭证名"
msgstr "Voucher name"

#: bkflow/space/models.py:250
msgid "空间凭证"
msgstr "Space voucher"

#: bkflow/space/models.py:251
msgid "空间凭证表"
msgstr "Space voucher table"

#: bkflow/space/serializers.py:36
msgid "平台地址"
msgstr "Platform address"

#: bkflow/task/utils.py:111
#, python-brace-format
msgid ""
"节点执行失败: 节点[ID: {act_id}]配置了非法的超时时间: {timeout_seconds}, 请修"
"改配置后重试"
msgstr ""
"Node execution failure: Node [ID: {act_id}] configure illegal timeout time: "
"{timeout_seconds}, please modify the configuration"

#: bkflow/template/exceptions.py:27
msgid "变量引用计算失败"
msgstr "Variable reference calculation failed"

#: bkflow/template/models.py:44
msgid "模板对应的数据ID"
msgstr "Data ID corresponding to the template"

#: bkflow/template/models.py:47
msgid "流程事件通知配置"
msgstr "Process event notification configuration"

#: bkflow/template/models.py:49
msgid "流程范围"
msgstr "Process"

#: bkflow/template/models.py:50
msgid "第三方系统对应的资源ID"
msgstr "Resource ID corresponding to the third -party system"

#: bkflow/template/models.py:52
msgid "是否启用"
msgstr "Whether to enable"

#: bkflow/template/models.py:53
msgid "额外的扩展信息"
msgstr "Additional expansion information"

#: bkflow/template/models.py:56
msgid "流程模板"
msgstr "Process template"

#: bkflow/template/models.py:57
msgid "流程模板信息表"
msgstr "Process template information table"

#: bkflow/template/models.py:121
msgid "模板快照"
msgstr "Template snapshot"

#: bkflow/template/models.py:122
msgid "模板快照表"
msgstr "Template express table"

#: bkflow/template/models.py:141 bkflow/template/models.py:142
msgid "模版操作记录"
msgstr "Template operation record"

#: bkflow/template/models.py:224 bkflow/template/models.py:241
#: bkflow/utils/models.py:35
msgid "更新时间"
msgstr "Update time"

#: bkflow/template/serializers.py:36 bkflow/template/serializers/template.py:77
#: bkflow/utils/models.py:66
msgid "快照ID"
msgstr "Snapshot ID"

#: bkflow/template/serializers.py:37 bkflow/template/serializers/template.py:78
msgid "配置"
msgstr "Configuration"

#: bkflow/template/serializers.py:43 bkflow/template/serializers/template.py:84
msgid "创建失败，对应的空间不存在"
msgstr "The creation failed, the corresponding space does not exist"

#: bkflow/template/serializers.py:53
#, fuzzy, python-brace-format
#| msgid "参数校验失败，pipeline校验不通过, err={}"
msgid "参数校验失败，pipeline校验不通过, err={msg}"
msgstr ""
"The parameter verification failed, the pipeline verification was not "
"approved, err = {msg}"

#: bkflow/template/serializers/template.py:79
msgid "版本"
msgstr "Version"

#: bkflow/template/serializers/template.py:80
msgid "流程说明"
msgstr "Flow Description"

#: bkflow/template/serializers/template.py:158
msgid "pipeline tree"
msgstr "pipeline Tree"

#: bkflow/template/serializers/template.py:159
msgid "画布宽度"
msgstr "Canvas width"

#: bkflow/template/serializers/template.py:178
msgid "模板ID列表"
msgstr "Template ID list"

#: bkflow/template/serializers/template.py:179
msgid "是否全量删除"
msgstr "Whether to delete it in full amount"

#: bkflow/template/serializers/template.py:202
msgid "mock数据名称"
msgstr "Mock data name"

#: bkflow/template/serializers/template.py:203
msgid "mock数据"
msgstr "MOCK data"

#: bkflow/template/serializers/template.py:204
msgid "是否为默认mock数据"
msgstr "Whether it is the default MOCK data"

#: bkflow/template/serializers/template.py:205
msgid "mock数据ID"
msgstr "Mock data ID"

#: bkflow/template/serializers/template.py:212
msgid "mock数据列表"
msgstr "Mock data list"

#: bkflow/template/serializers/template.py:229
msgid "包含的节点ID列表"
msgstr "The node ID list included"

#: bkflow/template/views/template.py:228
#, python-brace-format
msgid "流程自动排版失败: 流程排版发生异常: {e}, 请检查流程 | draw_pipeline"
msgstr ""
"Process automatic layout Failure: The process layout is abnormal: {e}, "
"please check the process | Draw_pipeline"

#: bkflow/utils/context.py:59
msgid "任务名称"
msgstr "mission name"

#: bkflow/utils/context.py:66
msgid "任务ID"
msgstr "Task ID"

#: bkflow/utils/context.py:71
msgid "任务开始时间"
msgstr "Time start time"

#: bkflow/utils/context.py:77
msgid "任务的执行人（点击开始执行的人员）"
msgstr "Executor of the task (click on the person who starts to execute)"

#: bkflow/utils/handlers.py:33
#, python-brace-format
msgid "调用{system}接口{api_name}返回失败, params={params}, error={error}"
msgstr ""
"Call the {system} interface {api_name} back failure, params = {params}, "
"error = {error}"

#: bkflow/utils/mixins.py:40 bkflow/utils/mixins.py:49
msgid "用户名不存在"
msgstr "Username does not exist"

#: bkflow/utils/models.py:36
msgid "修改人"
msgstr "Modified person"

#: bkflow/utils/models.py:37
msgid "是否软删除"
msgstr "Whether to soft delete"

#: bkflow/utils/models.py:67
msgid "快照字符串的md5sum"
msgstr "MD5SUM of Snapshot String"

#: bkflow/utils/models.py:69
msgid "存储的数据"
msgstr "Store data"

#: config/__init__.py:61
msgid "蓝鲸智云"
msgstr "Tencent BlueKing"

#: templates/403.html:6
msgid "系统权限不足"
msgstr "Insufficient system permissions"

#: templates/403.html:7
msgid "你的角色所拥有的权限不足"
msgstr "Your role has insufficient permissions"

#: templates/500.html:6
msgid "系统出现异常"
msgstr "System abnormalities"

#: templates/500.html:7
msgid "请记录下错误场景并及时与开发人员联系"
msgstr "Please record the wrong scene and contact the developer in time"

#: templates/base.html:42
msgid "蓝鲸开发框架(Django模板)"
msgstr "BlueKing Development Framework (Django template)"

#: templates/base.html:58
msgid "首页"
msgstr "front page"

#: templates/base.html:59
msgid "开发指引"
msgstr "Development Guideline"

#: templates/base.html:60
msgid "联系我们"
msgstr "contact us"

#: templates/base.html:63
msgid "语言"
msgstr "language"

#: templates/base.html:94
msgid "QQ咨询(800802001)"
msgstr "QQ Consultation (800802001)"

#: templates/base.html:96
msgid "蓝鲸论坛"
msgstr "BlueKing Forum"

#: templates/base.html:98
msgid "蓝鲸官网"
msgstr "BlueKing Official Website"

#: templates/base.html:99
msgid "蓝鲸智云工作台"
msgstr "BlueKing Workbench"

#: templates/base.html:102
msgid "蓝鲸智云 版权所有"
msgstr "BlueKing All Rights Reserved"

#, python-brace-format
#~ msgid "请求数据异常: {error}"
#~ msgstr "Request data exception: {error}"

#~ msgid "返回数据格式错误，不是合法 JSON 格式"
#~ msgstr "Returning the data format error is not a legal json format"

#, python-brace-format
#~ msgid "远程数据源数据转换失败: {error}"
#~ msgstr "Remote data source data conversion failure: {error}"

#~ msgid "密码"
#~ msgstr "password"

#~ msgid "密码变量"
#~ msgstr "Password variable"

#~ msgid "请注意，并非所有插件字段都支持密码变量的使用，请结合具体插件进行使用"
#~ msgstr ""
#~ "Please note that not all plug -in fields support the use of password "
#~ "variables, please use specific plug -ins"

#~ msgid "用户输入的密码加密后的值"
#~ msgstr "The value of the password entered by the user"

#~ msgid "APP Code"
#~ msgstr "App code"
