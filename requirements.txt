# -*- coding: utf-8 -*-
# Python 3.11 & Django 3.2 compatible requirements

# --- 蓝鲸/内部依赖 (围绕 Django 3.2 配置) ---
blueapps==4.15.8
apigw-manager[cryptography]==1.1.7
bamboo-pipeline==3.29.5
bk-notice-sdk==1.3.0
bkflow-feel==1.2.0
bkflow-dmn==0.2.0
bkflow-django-webhook==1.0.0

# --- Django 生态 ---
Django==3.2.25
djangorestframework==3.12.4
django-filter==23.5
django-cors-headers==3.13.0
django-extensions==3.2.3
drf-yasg==1.20.0

# --- Celery 生态 ---
celery==5.2.7
django-celery-beat==2.2.1
django-celery-results==2.4.0
eventlet==0.33.3
greenlet==2.0.2
django-timezone-field==4.2.3

# --- 核心工具库 ---
gunicorn==23.0.0
redis==4.6.0
requests==2.32.3
pymysql==1.1.1
pydantic==1.10.17
jsonschema==2.6.0
Werkzeug==1.0.1

# --- 加密与认证 ---
pyCryptodome==3.20.0
PyJWT==2.8.0
cryptography==42.0.8

# --- PaaS 增强服务 (Sentry) ---
# sentry-sdk[django]==2.0.1
raven==6.10.0

# --- 其他三方库 ---
boto3==1.34.129
jmespath==1.0.1
pytimeparse==1.1.8
tldextract==5.1.2
pyinstrument==4.6.2

# --- 高风险/可能不兼容的库 (重点测试!) ---
django-versionlog==1.6.0
django-dbconn-retry==0.1.5