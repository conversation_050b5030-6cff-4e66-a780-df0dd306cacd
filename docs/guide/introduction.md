# 产品简介

BKFlow 是一款面向平台、高效灵活的流程引擎平台，旨在助力接入系统快速获取流程执行能力。

![业务概览示意图](../pics/biz_view.png)

**三大核心功能服务**
- 流程编排和画布嵌入：直观地创建、编辑和管理流程，支持自定义权限控制。
- 流程任务执行能力：通过 API 实现流程任务的创建、执行和控制。
- 决策引擎能力：可在流程中进行规则管理和决策。

**多种接入和集成方式**

![接入场景示意图](../pics/cases.png)

为了满足接入系统不同的使用场景，BKFlow 支持多种不同的接入场景和集成方式：

- 接入场景
  - 个人使用场景
  - SaaS 集成场景
  - SaaS 定制场景
- 集成方式
  - Web 服务集成
  - SDK 集成

**接入系统扩展 & 管理**

为了满足接入系统灵活的业务和配置需求，BKFlow 提供了自定义扩展和数据管理能力：

- 高度可扩展&自定义：支持蓝鲸插件和 API 插件，满足各种接入系统业务场景上的自定义需求。
- 数据管理：提供空间数据管理端，接入系统管理员可基于空间视图进行资源数据管理和操作。
- Webhook 订阅机制：接入系统可以轻松感知，流程任务事件并进行自动化扩展。
- 计算和存储资源隔离：任务执行和数据可与其他接入空间隔离。
