default_stages: [ commit ]
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v2.1.0
    hooks:
      - id: check-merge-conflict
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]
  - repo: https://github.com/pycqa/flake8
    rev: 5.0.4
    hooks:
      - id: flake8
        language_version: python3
  - repo: https://github.com/alessandrojcm/commitlint-pre-commit-hook
    rev: v9.20.0
    hooks:
      - id: commitlint
        stages: [ commit-msg ]
        additional_dependencies: [ '@commitlint/config-conventional' ]
  #【可选】提升 Python 代码风格，符合更高版本的 Python 语法
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.0
    hooks:
    - id: pyupgrade
