# Git 版本控制
.git
.github
.gitignore

# Python 虚拟环境
.venv
.venv1

# Node.js 依赖
/frontend/node_modules

# IDE 和编辑器配置
.idea
.vscode
*.sublime-project
*.sublime-workspace

# macOS 系统文件
.DS_Store

# 日志和临时文件
*.log
/v3logs/
/logs/

# Python 缓存
__pycache__/
*.pyc
*.pyo
*.pyd

# 测试和覆盖率报告
.pytest_cache/
.coverage
htmlcov/

# 其他本地配置和敏感文件
.env
local_settings.py
*.sqlite3

# Pre-commit 和 linting 工具配置
.pre-commit-config.yaml
.flake8
commitlint.config.js
pyproject.toml
pytest.ini
bkcodeai.json

# 构建产物 (如果本地有的话)
# /staticfiles/
/dist/
/build/

# 其他不必要的文件
Aptfile
runtime.txt
README.md