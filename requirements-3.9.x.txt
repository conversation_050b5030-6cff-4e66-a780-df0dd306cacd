# -*- coding: utf-8 -*-
# 工程预装模块
# 需要额外的python包，可直接在文件后面添加
# 请确保指定的包和版本号，可通过pip安装

# blueapps requirement
redis==4.1.4
pymysql==1.1.1
Django==3.2.25
celery==5.2.3
eventlet==0.33.3
greenlet==1.1.3
django-celery-beat==2.2.0
django-celery-results==2.4.0
django-cors-headers==3.7.0
djangorestframework==3.12.4
django-filter==2.4.0
drf-yasg==1.20.0
blueapps==4.15.8
gunicorn==23.0.0

# jwt
pyCryptodome==3.19.1
PyJWT==2.4.0
cryptography==42.0.4

# PaaS 增强服务需要的依赖包，请不要修改，否则可能导增强服务不可用
# for sentry
raven==6.10.0

# interface
jsonschema==2.5.1
django-versionlog==1.6.0
bk-notice-sdk==1.3.0

# engine service
boto3==1.26.133
bamboo-pipeline==3.29.5
pydantic==1.10.6
django-extensions==3.2.1

# bkflow feel
bkflow-feel==1.2.0
bkflow-dmn==0.2.0
bkflow-django-webhook==1.0.0

# pytimeparse
pytimeparse == 1.1.8
django-filter==2.4.0

apigw-manager[cryptography]==1.1.7

Werkzeug==1.0.1
requests==2.28.2
django-dbconn-retry==0.1.5
jmespath==1.0.1
pyinstrument==3.1.3
tldextract==3.1.2
