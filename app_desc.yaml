spec_version: 2
app_version: "1.11.1"
app:
  region: default
  bk_app_code: &APP_CODE bk_flow_engine
  bk_app_name: 蓝鲸流程引擎服务
  bk_app_name_en: BKFlow
  market:
    category: 运维工具
    introduction: 流程引擎服务
    introduction_en: Process Engine Service
    description: 为上层应用提供快捷接入流程引擎服务能力
    description_en: Provide upper-level applications with quick access to process engine service capabilities
    display_options:
      width: 1280
      height: 720
      is_win_maximize: true
      open_mode: "new_tab"
modules:
  default:
    is_default: True
    language: Python
    source_dir: ./src
    services:
      - name: mysql
      - name: rabbitmq
    scripts:
      pre_release_hook: bin/pre_release.sh
    env_variables:
      - &ENV_PIP_VERSION
        key: PIP_VERSION
        value: 23.0.1
        description: pip版本
      - key: BKFLOW_MODULE_TYPE
        value: interface
        description: 模块类型
      - key: GUNICORN_WORKER_NUM
        value: 1
        description: GunicornWorker数量
      - key: GUNICORN_THREAD_NUM
        value: 10
        description: GunicornThread数量
      - key: BK_APIGW_NAME
        value: *APP_CODE
        description: APIGW名称
      - &ENV_APP_INTERNAL_TOKEN
        key: APP_INTERNAL_TOKEN
        value: &INTERFACE_INTERNAL_TOKEN df1da1fc5b134858b0743838d7db256b
        description: 应用模块内部调用token
      - key: DEFAULT_ENGINE_APP_INTERNAL_TOKEN
        value: &DEFAULT_ENGINE_INTERNAL_TOKEN a35230d7a282485dba66a867e07d8125
        description: default-engine模块内部调用token
      - &ENV_BK_APIGW_NETLOC_PATTERN
        key: BK_APIGW_NETLOC_PATTERN
        value: ^bkapi.{hostname}
        description: 用于识别请求地址是否来自 APIGW
      - &ENV_BKAPP_USE_PLUGIN_SERVICE
        key: BKAPP_USE_PLUGIN_SERVICE
        value: 1
        description: 是否使用插件服务
      - &DEFAULT_CALLBACK_KEY
        key: BKFLOW_DEFAULT_CALLBACK_KEY
        value: jbSH1_3PFsM8WRZZpUXJPhlJuvuA44A7Ov0nPhFk5ZY=
        description: 默认回调KEY
    processes:
      web:
        command: bin/start_web.sh
        plan: 4C2G5R
        replicas: 5
      beat:
        command: celery -A blueapps.core.celery beat -l info
        plan: 4C1G5R
        replicas: 1
      worker:
        command: celery -A blueapps.core.celery worker -n interface_worker@%h -P threads -c 100 -l info
        plan: 4C2G5R
        replicas: 2
    svc_discovery:
      bk_saas:
        - bk_app_code: 'bk-user'
        - bk_app_code: *APP_CODE
        - bk_app_code: *APP_CODE
          module_name: 'default-engine'
  default-engine:
    is_default: False
    language: Python
    source_dir: ./src
    scripts:
      pre_release_hook: bin/pre_release.sh
    services:
      - name: mysql
      - name: rabbitmq
      - name: redis
        spec: reusable
    svc_discovery:
      bk_saas:
        - *APP_CODE
    env_variables:
      - key: BKFLOW_MODULE_TYPE
        value: engine
        description: 模块类型
      - key: BKFLOW_MODULE_CODE
        value: default
        description: 空间编码
      - key: GUNICORN_WORKER_NUM
        value: 2
        description: GunicornWorker数量
      - key: GUNICORN_THREAD_NUM
        value: 10
        description: GunicornThread数量
      - key: INTERFACE_APP_INTERNAL_TOKEN
        value: *INTERFACE_INTERNAL_TOKEN
        description: Interface模块内部调用token
      - <<: *ENV_PIP_VERSION
      - <<: *ENV_BK_APIGW_NETLOC_PATTERN
      - <<: *ENV_BKAPP_USE_PLUGIN_SERVICE
      - <<: *DEFAULT_CALLBACK_KEY
      - <<: *ENV_APP_INTERNAL_TOKEN
        value: *DEFAULT_ENGINE_INTERNAL_TOKEN
    processes: &default_engine_processes
      er-e:
        command: celery -A blueapps.core.celery worker -P threads -Q er_execute_${BKFLOW_MODULE_CODE} -n er_e_worker@%h -c 100 -l info
        plan: 4C4G5R
        replicas: 5
      er-s:
        command: celery -A blueapps.core.celery worker -P threads -Q er_schedule_${BKFLOW_MODULE_CODE} -n er_s_worker@%h -c 100 -l info
        plan: 4C4G5R
        replicas: 5
      cworker:
        command: python manage.py celery worker -Q celery,pipeline_additional_task,pipeline_additional_task_priority,task_common_${BKFLOW_MODULE_CODE},node_auto_retry_${BKFLOW_MODULE_CODE},timeout_node_execute_${BKFLOW_MODULE_CODE},timeout_node_record_${BKFLOW_MODULE_CODE} -n common_worker@%h -P threads -c 10 -l info
        plan: 4C1G5R
        replicas: 5
      timeout:
        command: python manage.py node_timeout_process
        plan: 4C1G5R
        replicas: 1
      web:
        command: bin/start_web.sh
        plan: 4C2G5R
        replicas: 5
      beat:
        command: celery -A blueapps.core.celery beat -l info
        plan: 4C1G5R
        replicas: 1
      clean-worker:
        command: celery -A blueapps.core.celery worker -P threads -Q clean_task_${BKFLOW_MODULE_CODE} -n clean_worker@%h -c 100 -l info
        plan: 4C4G5R
        replicas: 1