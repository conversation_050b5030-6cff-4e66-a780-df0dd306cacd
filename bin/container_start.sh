#!/bin/bash
# -----------------------------------------------------------------------------
# 容器启动脚本 (在 Docker 运行期间运行)
# -----------------------------------------------------------------------------

# 如果任何命令失败，立即退出
set -e

#!/bin/bash

# 如果任何命令失败，脚本将立即退出
set -e

echo "--- [Entrypoint] Running pre-start tasks... ---"

echo "Running database migrations..."
python manage.py migrate

echo "Creating cache table..."
python manage.py createcachetable django_cache

echo "Updating models..."
python manage.py update_component_models
python manage.py update_variable_models

# 检查环境变量并执行相应的同步任务
if [ "$BKPAAS_APP_MODULE_NAME" == "default" ]; then
    echo "Module is 'default', running sync tasks for SaaS..."
    python manage.py sync_saas_apigw
    python manage.py sync_superuser
    python manage.py sync_default_module
    python manage.py register_bkflow_to_bknotice
    python manage.py sync_webhook_events ./webhook_resources.yaml
else
    echo "Current module is not 'default' ($BKPAAS_APP_MODULE_NAME), skipping SaaS sync tasks."
fi

echo "--- [Entrypoint] Pre-start tasks complete. ---"

# 为 Gunicorn 参数设置默认值 (如果环境变量没有提供)
: "${GUNICORN_WORKER_NUM:=4}"  # 默认 4 个 worker 进程
: "${GUNICORN_THREAD_NUM:=2}"  # 默认每个 worker 2 个线程
: "${PORT:=5000}"             # 默认端口 5000

echo "---> [start_web] Starting Gunicorn on port ${PORT}..."
echo "---> [start_web] Workers: ${GUNICORN_WORKER_NUM}, Threads per worker: ${GUNICORN_THREAD_NUM}"

python manage.py collectstatic --noinput

# 使用 exec 启动 Gunicorn
# "exec" 会让 Gunicorn 进程替换掉当前的 shell 进程，
# 这样做的好处是 Gunicorn 可以直接接收来自 Docker 的信号 (如 SIGTERM)，实现优雅停机。
exec gunicorn wsgi \
    -w "$GUNICORN_WORKER_NUM" \
    -b "[::]:${PORT}" \
    -k gthread \
    --threads "$GUNICORN_THREAD_NUM" \
    --access-logfile - \
    --error-logfile - \
    --access-logformat '[%(h)s] %({request_id}i)s %(u)s %(t)s "%(r)s" %(s)s %(D)s %(b)s "%(f)s" "%(a)s"' \
    --max-requests=500